# 🗄️ Database Connection Information for Abu Dhabi OMOP Database

## 📋 Connection Details

### PostgreSQL Database Configuration
| Parameter | Value | Description |
|-----------|-------|-------------|
| **Host** | `localhost` | Database server address |
| **Port** | `5432` | PostgreSQL default port |
| **Database Name** | `omop_abu_dhabi` | OMOP database for Abu Dhabi claims |
| **Username** | `jaimepm` | Database owner/user |
| **Password** | *(empty/no password)* | No password authentication (local user) |
| **Schema** | `public` | Default PostgreSQL schema |
| **Connection Type** | Socket | Local socket connection via `/tmp` |

### Connection String Examples

#### Standard PostgreSQL Connection String
```
postgresql://jaimepm@localhost:5432/omop_abu_dhabi
```

#### With explicit empty password
```
postgresql://jaimepm:@localhost:5432/omop_abu_dhabi
```

#### JDBC Connection String (for tools like DBeaver)
```
***********************************************
```

## 🔌 Database Viewer Tools Recommended

### 1. **pgAdmin** (PostgreSQL Native Tool)
- **Download**: https://www.pgadmin.org/download/
- **Connection Setup**:
  - Server: localhost
  - Port: 5432
  - Database: omop_abu_dhabi
  - Username: jaimepm
  - Password: (leave empty)

### 2. **DBeaver** (Universal Database Tool)
- **Download**: https://dbeaver.io/download/
- **Connection Setup**:
  - Database Type: PostgreSQL
  - Server Host: localhost
  - Port: 5432
  - Database: omop_abu_dhabi
  - Username: jaimepm
  - Password: (leave empty)

### 3. **TablePlus** (macOS/Windows)
- **Download**: https://tableplus.com/
- **Connection Setup**:
  - Type: PostgreSQL
  - Host: localhost
  - Port: 5432
  - Database: omop_abu_dhabi
  - User: jaimepm
  - Password: (leave empty)

### 4. **Adminer** (Web-based)
- **Access**: Via browser at http://localhost:8080 (if running with Docker)
- **Connection Setup**:
  - System: PostgreSQL
  - Server: localhost:5432
  - Username: jaimepm
  - Password: (leave empty)
  - Database: omop_abu_dhabi

## 📊 Database Structure Overview

### Current OMOP Tables
| Table Name | Purpose | Status |
|------------|---------|--------|
| `person` | Patient demographics | ✅ Created (0 rows) |
| `visit_occurrence` | Healthcare encounters | ✅ Created (0 rows) |
| `procedure_occurrence` | Medical procedures (CPT codes) | ✅ Created (0 rows) |
| `provider` | Healthcare providers | ✅ Created (0 rows) |
| `cost` | Financial information | ✅ Created (0 rows) |

### Data Status
- **Tables Created**: ✅ All core OMOP tables present
- **Data Loaded**: ⚠️ Tables are empty (ready for ETL)
- **Schema**: Standard OMOP CDM v5.4 structure
- **Owner**: `jaimepm` (full permissions)

## 🛠️ Quick Connection Test

### Command Line Test
```bash
# Test basic connection
psql -d omop_abu_dhabi -c "SELECT current_database(), current_user, version();"

# List all tables
psql -d omop_abu_dhabi -c "\dt"

# Check table structure
psql -d omop_abu_dhabi -c "\d person"
```

### SQL Test Queries
```sql
-- Basic connection test
SELECT current_database(), current_user, now();

-- Check table existence
SELECT schemaname, tablename 
FROM pg_tables 
WHERE schemaname = 'public' 
ORDER BY tablename;

-- Verify OMOP structure
SELECT column_name, data_type 
FROM information_schema.columns 
WHERE table_name = 'person' 
ORDER BY ordinal_position;
```

## 🔧 Troubleshooting

### If Connection Fails:
1. **Verify PostgreSQL is running**:
   ```bash
   brew services list | grep postgresql
   # or
   ps aux | grep postgres
   ```

2. **Check if database exists**:
   ```bash
   psql -l | grep omop_abu_dhabi
   ```

3. **Test basic PostgreSQL connection**:
   ```bash
   psql -d postgres -c "SELECT version();"
   ```

### Common Issues:
- **"database does not exist"**: Database name is case-sensitive
- **"role does not exist"**: Username must match your system user
- **"permission denied"**: Ensure you have proper database privileges
- **"connection refused"**: PostgreSQL service may not be running

## 📝 Next Steps

1. **Choose a database viewer** from the recommended tools above
2. **Configure connection** using the provided parameters
3. **Explore OMOP structure** to understand the data model
4. **Run ETL process** to populate tables with Abu Dhabi claims data
5. **Visualize and analyze** your healthcare data

## 🎯 Use Cases for Database Visualization

### Data Analysis
- Explore patient demographics (when data is loaded)
- Analyze visit patterns and healthcare utilization
- Review procedure codes and their frequencies
- Examine cost patterns and financial metrics

### OMOP Learning
- Understand OMOP CDM table relationships
- Explore foreign key constraints
- Review data types and field meanings
- Validate ETL results

### Quality Assurance
- Check data completeness
- Validate referential integrity
- Monitor ETL process results
- Identify data quality issues

---

**Created**: $(date)  
**Database Version**: PostgreSQL $(psql --version | cut -d' ' -f3)  
**OMOP CDM Version**: 5.4  
**Project**: Abu Dhabi Claims OMOP Implementation
