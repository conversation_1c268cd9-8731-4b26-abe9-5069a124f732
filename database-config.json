{"database_connections": {"omop_abu_dhabi": {"name": "Abu Dhabi OMOP Database", "type": "postgresql", "host": "localhost", "port": 5432, "database": "omop_abu_dhabi", "username": "jai<PERSON><PERSON>", "password": "", "schema": "public", "ssl_mode": "disable", "connection_timeout": 30, "description": "OMOP CDM database for Abu Dhabi healthcare claims analysis", "tags": ["omop", "healthcare", "abu_dhabi", "claims"]}}, "connection_strings": {"postgresql": "postgresql://jaimepm@localhost:5432/omop_abu_dhabi", "postgresql_with_password": "postgresql://jaimepm:@localhost:5432/omop_abu_dhabi", "jdbc": "***********************************************", "sqlalchemy": "postgresql://jaimepm@localhost:5432/omop_abu_dhabi"}, "database_info": {"version": "PostgreSQL 17.4", "omop_version": "5.4", "encoding": "UTF8", "collation": "C", "ctype": "C", "owner": "jai<PERSON><PERSON>", "tables_count": 5, "data_loaded": false}, "tables": [{"name": "person", "purpose": "Patient demographics and basic information", "row_count": 0, "primary_key": "person_id"}, {"name": "visit_occurrence", "purpose": "Healthcare encounters and visits", "row_count": 0, "primary_key": "visit_occurrence_id"}, {"name": "procedure_occurrence", "purpose": "Medical procedures and CPT codes", "row_count": 0, "primary_key": "procedure_occurrence_id"}, {"name": "provider", "purpose": "Healthcare providers and practitioners", "row_count": 0, "primary_key": "provider_id"}, {"name": "cost", "purpose": "Financial and cost information", "row_count": 0, "primary_key": "cost_id"}], "recommended_tools": [{"name": "pgAdmin", "type": "native", "platform": "cross-platform", "url": "https://www.pgadmin.org/", "description": "Official PostgreSQL administration tool"}, {"name": "DBeaver", "type": "universal", "platform": "cross-platform", "url": "https://dbeaver.io/", "description": "Universal database tool with excellent PostgreSQL support"}, {"name": "TablePlus", "type": "modern", "platform": "macOS/Windows", "url": "https://tableplus.com/", "description": "Modern database management tool with clean interface"}, {"name": "Ad<PERSON>r", "type": "web", "platform": "web-based", "url": "https://www.adminer.org/", "description": "Web-based database administration tool"}], "test_queries": [{"name": "Connection Test", "query": "SELECT current_database(), current_user, version();", "purpose": "Verify basic database connection"}, {"name": "Table List", "query": "SELECT schemaname, tablename FROM pg_tables WHERE schemaname = 'public' ORDER BY tablename;", "purpose": "List all OMOP tables"}, {"name": "Person Table Structure", "query": "SELECT column_name, data_type, is_nullable FROM information_schema.columns WHERE table_name = 'person' ORDER BY ordinal_position;", "purpose": "Examine person table structure"}, {"name": "Data Counts", "query": "SELECT 'person' as table_name, COUNT(*) as row_count FROM person UNION ALL SELECT 'visit_occurrence', COUNT(*) FROM visit_occurrence UNION ALL SELECT 'procedure_occurrence', COUNT(*) FROM procedure_occurrence;", "purpose": "Check data counts in main tables"}], "metadata": {"created": "2024-12-19", "project": "FHIR-OMOP Transformation Pipeline", "use_case": "Abu Dhabi Claims Analysis", "status": "Ready for ETL", "last_updated": "2024-12-19"}}