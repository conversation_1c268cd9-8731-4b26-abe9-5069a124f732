#!/usr/bin/env python3
"""
Análisis Específico de Códigos Médicos - Abu Dhabi Claims
==========================================================

Script para análisis detallado de códigos médicos sin documentación previa.
Identifica patrones, categoriza códigos y evalúa mapeo OMOP.

Autor: Equipo FHIR-OMOP
Fecha: Mayo 2025
"""

import pandas as pd
import numpy as np
import re
from collections import Counter, defaultdict
import json

class MedicalCodeAnalyzer:
    """
    Analizador de códigos médicos para datasets sin documentación
    """
    
    def __init__(self, df):
        self.df = df
        self.code_patterns = {}
        self.code_categories = {}
        
    def identify_code_patterns(self):
        """
        Identifica patrones en códigos médicos sin documentación previa
        """
        print("🔍 IDENTIFICANDO PATRONES DE CÓDIGOS MÉDICOS")
        print("="*60)
        
        codes = self.df['code_activity'].dropna().astype(str)
        
        patterns = {
            'cpt_5_digit': r'^\d{5}$',           # CPT estándar
            'cpt_4_digit': r'^\d{4}$',           # CPT modificado
            'icd_10': r'^[A-Z]\d{2}\.?\d*$',     # ICD-10
            'drug_code_uae': r'^[A-Z]\d+-\d+-\d+-\d+$',  # Patrón UAE: B46-4387-00778-01
            'alphanumeric': r'^[A-Z]+\d+$',      # Códigos alfanuméricos
            'numeric_long': r'^\d{6,}$',         # Números largos
            'mixed_complex': r'^[A-Z0-9-]+$',    # Códigos complejos con guiones
        }
        
        results = {}
        for pattern_name, pattern in patterns.items():
            matches = codes.str.match(pattern, na=False)
            matching_codes = codes[matches]
            results[pattern_name] = {
                'count': len(matching_codes),
                'percentage': len(matching_codes) / len(codes) * 100,
                'examples': matching_codes.head(10).tolist(),
                'unique_count': matching_codes.nunique()
            }
            
        self.code_patterns = results
        return results
    
    def analyze_cpt_codes(self):
        """
        Análisis específico de códigos CPT identificados
        """
        print("\n⚕️ ANÁLISIS DETALLADO DE CÓDIGOS CPT")
        print("="*60)
        
        # Filtrar códigos que parecen CPT
        potential_cpt = self.df[
            self.df['code_activity'].astype(str).str.match(r'^\d{5}$', na=False)
        ].copy()
        
        if len(potential_cpt) == 0:
            print("❌ No se encontraron códigos con patrón CPT estándar")
            return {}
        
        print(f"📊 CÓDIGOS CPT IDENTIFICADOS: {len(potential_cpt):,} registros")
        print(f"📊 CÓDIGOS CPT ÚNICOS: {potential_cpt['code_activity'].nunique():,}")
        
        # Analizar rangos CPT conocidos
        cpt_ranges = {
            'Evaluation & Management': (99201, 99499),
            'Anesthesiology': (0, 1999),
            'Surgery': (10000, 69999),
            'Radiology': (70000, 79999),
            'Pathology & Laboratory': (80000, 89999),
            'Medicine': (90000, 99199)
        }
        
        cpt_analysis = {}
        for category, (min_code, max_code) in cpt_ranges.items():
            codes_in_range = potential_cpt[
                (potential_cpt['code_activity'].astype(int) >= min_code) & 
                (potential_cpt['code_activity'].astype(int) <= max_code)
            ]
            
            if len(codes_in_range) > 0:
                cpt_analysis[category] = {
                    'count': len(codes_in_range),
                    'unique_codes': codes_in_range['code_activity'].nunique(),
                    'top_codes': codes_in_range['code_activity'].value_counts().head(5).to_dict(),
                    'percentage': len(codes_in_range) / len(potential_cpt) * 100
                }
                
                print(f"\n📋 {category}:")
                print(f"   Registros: {len(codes_in_range):,} ({cpt_analysis[category]['percentage']:.1f}%)")
                print(f"   Códigos únicos: {codes_in_range['code_activity'].nunique()}")
                
                # Mostrar top códigos con descripción
                for code, count in list(cpt_analysis[category]['top_codes'].items())[:3]:
                    desc = codes_in_range[codes_in_range['code_activity'] == code]['activity_desc'].iloc[0]
                    print(f"   • {code}: {count:,} veces - {desc[:50]}...")
        
        return cpt_analysis
    
    def analyze_uae_codes(self):
        """
        Análisis específico de códigos UAE identificados
        """
        print("\n🇦🇪 ANÁLISIS DE CÓDIGOS UAE ESPECÍFICOS")
        print("="*60)
        
        # Buscar códigos con patrones típicos de UAE
        uae_patterns = {
            'drug_format': r'^[A-Z]\d+-\d+-\d+-\d+$',  # B46-4387-00778-01
            'alpha_numeric': r'^[A-Z]+\d+$',            # GD11650
            'complex_mixed': r'^[A-Z0-9-]+$'            # Otros patrones complejos
        }
        
        uae_results = {}
        for pattern_name, pattern in uae_patterns.items():
            matches = self.df['code_activity'].astype(str).str.match(pattern, na=False)
            matching_records = self.df[matches]
            
            if len(matching_records) > 0:
                uae_results[pattern_name] = {
                    'count': len(matching_records),
                    'unique_codes': matching_records['code_activity'].nunique(),
                    'examples': matching_records['code_activity'].unique()[:5].tolist(),
                    'descriptions': []
                }
                
                # Obtener descripciones
                for code in uae_results[pattern_name]['examples']:
                    desc = matching_records[matching_records['code_activity'] == code]['activity_desc'].iloc[0]
                    uae_results[pattern_name]['descriptions'].append({
                        'code': code,
                        'description': desc
                    })
                
                print(f"\n📋 PATRÓN {pattern_name.upper()}:")
                print(f"   Registros: {len(matching_records):,}")
                print(f"   Códigos únicos: {matching_records['code_activity'].nunique()}")
                print(f"   Ejemplos:")
                for item in uae_results[pattern_name]['descriptions']:
                    print(f"   • {item['code']}: {item['description'][:60]}...")
        
        return uae_results
    
    def categorize_activities(self):
        """
        Categoriza actividades basándose en descripciones
        """
        print("\n📋 CATEGORIZACIÓN DE ACTIVIDADES POR DESCRIPCIÓN")
        print("="*60)
        
        # Palabras clave para categorización
        categories = {
            'laboratory': ['blood', 'urine', 'test', 'culture', 'specimen', 'antigen', 'antibody'],
            'imaging': ['x-ray', 'ultrasound', 'mri', 'ct', 'scan', 'radiologic', 'echo'],
            'surgery': ['surgical', 'incision', 'excision', 'repair', 'reconstruction', 'procedure'],
            'consultation': ['visit', 'consultation', 'evaluation', 'management', 'office', 'outpatient'],
            'therapy': ['therapy', 'therapeutic', 'treatment', 'exercise', 'rehabilitation', 'physical'],
            'medication': ['injection', 'infusion', 'drug', 'medication', 'pharmaceutical'],
            'emergency': ['emergency', 'urgent', 'trauma', 'critical', 'icu'],
            'preventive': ['vaccination', 'screening', 'prevention', 'check-up', 'wellness']
        }
        
        # Crear columna de categoría
        self.df['activity_category'] = 'other'
        
        category_results = {}
        for category, keywords in categories.items():
            # Buscar en descripciones (case insensitive)
            pattern = '|'.join(keywords)
            matches = self.df['activity_desc'].str.contains(pattern, case=False, na=False)
            
            self.df.loc[matches, 'activity_category'] = category
            count = matches.sum()
            
            if count > 0:
                category_results[category] = {
                    'count': count,
                    'percentage': count / len(self.df) * 100,
                    'top_codes': self.df[matches]['code_activity'].value_counts().head(3).to_dict()
                }
                
                print(f"\n📊 {category.upper()}: {count:,} registros ({category_results[category]['percentage']:.1f}%)")
                for code, freq in list(category_results[category]['top_codes'].items())[:3]:
                    desc = self.df[self.df['code_activity'] == code]['activity_desc'].iloc[0]
                    print(f"   • {code}: {freq:,} veces - {desc[:50]}...")
        
        # Mostrar actividades sin categorizar
        uncategorized = self.df[self.df['activity_category'] == 'other']
        print(f"\n❓ SIN CATEGORIZAR: {len(uncategorized):,} registros ({len(uncategorized)/len(self.df)*100:.1f}%)")
        
        return category_results
    
    def generate_omop_mapping_strategy(self):
        """
        Genera estrategia de mapeo OMOP basada en análisis
        """
        print("\n🎯 ESTRATEGIA DE MAPEO OMOP")
        print("="*60)
        
        mapping_strategy = {
            'procedure_occurrence': {
                'source_codes': [],
                'mapping_approach': 'CPT to SNOMED mapping via Athena',
                'confidence': 'High',
                'estimated_success': '80%'
            },
            'drug_exposure': {
                'source_codes': [],
                'mapping_approach': 'UAE codes to RxNorm via Shafafiya Dictionary',
                'confidence': 'Medium',
                'estimated_success': '60%'
            },
            'measurement': {
                'source_codes': [],
                'mapping_approach': 'Lab codes to LOINC',
                'confidence': 'Medium',
                'estimated_success': '70%'
            }
        }
        
        # Clasificar códigos por dominio OMOP probable
        if hasattr(self, 'code_patterns'):
            # CPT codes -> PROCEDURE_OCCURRENCE
            if 'cpt_5_digit' in self.code_patterns:
                mapping_strategy['procedure_occurrence']['source_codes'] = self.code_patterns['cpt_5_digit']['examples']
            
            # UAE drug codes -> DRUG_EXPOSURE
            if 'drug_code_uae' in self.code_patterns:
                mapping_strategy['drug_exposure']['source_codes'] = self.code_patterns['drug_code_uae']['examples']
        
        # Mostrar estrategia
        for omop_domain, strategy in mapping_strategy.items():
            print(f"\n📋 {omop_domain.upper()}:")
            print(f"   Enfoque: {strategy['mapping_approach']}")
            print(f"   Confianza: {strategy['confidence']}")
            print(f"   Éxito estimado: {strategy['estimated_success']}")
            if strategy['source_codes']:
                print(f"   Códigos ejemplo: {', '.join(strategy['source_codes'][:3])}...")
        
        return mapping_strategy
    
    def export_analysis_results(self, output_path):
        """
        Exporta resultados del análisis
        """
        results = {
            'dataset_summary': {
                'total_records': len(self.df),
                'unique_codes': self.df['code_activity'].nunique(),
                'unique_patients': self.df['aio_patient_id'].nunique()
            },
            'code_patterns': getattr(self, 'code_patterns', {}),
            'category_analysis': getattr(self, 'category_results', {}),
            'analysis_timestamp': pd.Timestamp.now().isoformat()
        }
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        print(f"\n💾 Resultados exportados a: {output_path}")

def main():
    """
    Función principal de análisis
    """
    print("🚀 INICIANDO ANÁLISIS DE CÓDIGOS MÉDICOS ABU DHABI")
    print("="*70)
    
    # Cargar dataset
    file_path = '../../../data/real_test_datasets/claim_anonymized.csv'
    try:
        df = pd.read_csv(file_path)
        print(f"✅ Dataset cargado: {len(df):,} registros")
    except FileNotFoundError:
        print(f"❌ No se pudo encontrar el archivo: {file_path}")
        return
    
    # Crear analizador
    analyzer = MedicalCodeAnalyzer(df)
    
    # Ejecutar análisis
    print("\n" + "="*70)
    analyzer.identify_code_patterns()
    
    print("\n" + "="*70)
    analyzer.analyze_cpt_codes()
    
    print("\n" + "="*70)
    analyzer.analyze_uae_codes()
    
    print("\n" + "="*70)
    category_results = analyzer.categorize_activities()
    analyzer.category_results = category_results
    
    print("\n" + "="*70)
    analyzer.generate_omop_mapping_strategy()
    
    # Exportar resultados
    output_path = 'medical_codes_analysis_results.json'
    analyzer.export_analysis_results(output_path)
    
    print("\n" + "="*70)
    print("✅ ANÁLISIS COMPLETADO")
    print("📋 Próximos pasos:")
    print("   1. Revisar patrones CPT identificados")
    print("   2. Investigar códigos UAE con Shafafiya Dictionary")
    print("   3. Validar categorización de actividades")
    print("   4. Preparar mapeo OMOP basado en hallazgos")

if __name__ == "__main__":
    main()
