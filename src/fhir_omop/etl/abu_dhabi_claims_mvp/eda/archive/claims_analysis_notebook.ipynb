{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Abu Dhabi Claims Dataset - Exploratory Data Analysis\n", "\n", "**Objective**: Comprehensive analysis of the Abu Dhabi claims dataset to understand data structure, quality, and mapping requirements for OMOP CDM implementation.\n", "\n", "**Dataset**: `claim_anonymized.csv` - Initial dataset provided by Abu Dhabi client\n", "\n", "**Context**: This is an initial dataset from the client, so we need to identify all limitations and data quality issues for discussion with the team.\n", "\n", "---"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Setup and Data Loading"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Libraries imported successfully\n", "📊 Ready for Abu Dhabi Claims EDA\n"]}], "source": ["# Import essential libraries\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import plotly.express as px\n", "import plotly.graph_objects as go\n", "from plotly.subplots import make_subplots\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Set up plotting\n", "plt.style.use('default')\n", "sns.set_palette(\"husl\")\n", "pd.set_option('display.max_columns', None)\n", "pd.set_option('display.max_rows', 100)\n", "\n", "print(\"✅ Libraries imported successfully\")\n", "print(\"📊 Ready for Abu Dhabi Claims EDA\")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Dataset loaded successfully\n", "📊 Shape: (4999, 54)\n", "📋 Columns: 54\n", "💾 Memory usage: 11.28 MB\n"]}], "source": ["# Load the dataset\n", "data_path = \"../../../../../data/real_test_datasets/claim_anonymized.csv\"\n", "\n", "try:\n", "    df = pd.read_csv(data_path)\n", "    print(f\"✅ Dataset loaded successfully\")\n", "    print(f\"📊 Shape: {df.shape}\")\n", "    print(f\"📋 Columns: {len(df.columns)}\")\n", "    print(f\"💾 Memory usage: {df.memory_usage(deep=True).sum() / 1024**2:.2f} MB\")\n", "except FileNotFoundError:\n", "    print(f\"❌ Dataset not found at: {data_path}\")\n", "    print(\"Please check the file path and try again.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Dataset Overview and Structure"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🏥 ABU DHABI CLAIMS DATASET OVERVIEW\n", "============================================================\n", "Total records: 4,999\n", "Total columns: 54\n", "Date range: 01/01/2023 to 31/12/2023\n", "Memory usage: 11.28 MB\n", "\n", "📋 COLUMN OVERVIEW:\n", " 1. provider_id\n", " 2. institution_name\n", " 3. case_type\n", " 4. claim_id\n", " 5. claim_net\n", " 6. unique_id\n", " 7. case\n", " 8. insurance_plan_id\n", " 9. plan_name\n", "10. network_name\n", "11. payer_id\n", "12. payer_id_desc\n", "13. id_payer\n", "14. denial_code\n", "15. code_activity\n", "16. activity_desc\n", "17. activity_id\n", "18. reference_activity\n", "19. start_activity_date\n", "20. type_activity\n", "21. act_type_desc\n", "22. activity_quantity\n", "23. mapping_status\n", "24. claim_mapping_status\n", "25. gross\n", "26. patient_share\n", "27. net\n", "28. payment_amount\n", "29. rejected_amount\n", "30. resub_net\n", "31. clinician\n", "32. clinician_name\n", "33. resub_date\n", "34. remittance_date\n", "35. ra_aging\n", "36. resub_aging\n", "37. claim_status_desc\n", "38. resub_type_desc\n", "39. encounter_start_type\n", "40. encounter_start_type_desc\n", "41. encounter_start_date\n", "42. encounter_end_date\n", "43. encounter_end_type\n", "44. encounter_end_type_desc\n", "45. receiver_id\n", "46. receiver_id_desc\n", "47. prior_authorization\n", "48. submission_date\n", "49. processing_status\n", "50. accepted_type\n", "51. accepted_type_reason_items\n", "52. reconciliation_claim_tag\n", "53. year_encounter_end_date\n", "54. aio_patient_id\n"]}], "source": ["# Basic dataset information\n", "print(\"🏥 ABU DHABI CLAIMS DATASET OVERVIEW\")\n", "print(\"=\" * 60)\n", "print(f\"Total records: {len(df):,}\")\n", "print(f\"Total columns: {len(df.columns)}\")\n", "print(f\"Date range: {df['encounter_start_date'].min()} to {df['encounter_start_date'].max()}\")\n", "print(f\"Memory usage: {df.memory_usage(deep=True).sum() / 1024**2:.2f} MB\")\n", "\n", "print(\"\\n📋 COLUMN OVERVIEW:\")\n", "for i, col in enumerate(df.columns, 1):\n", "    print(f\"{i:2d}. {col}\")"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔍 DATA TYPES AND COMPLETENESS ANALYSIS\n", "============================================================\n", "Key Fields Analysis:\n", "  aio_patient_id:\n", "    - Completeness: 100.0%\n", "    - Unique values: 596\n", "    - Data type: object\n", "\n", "  case:\n", "    - Completeness: 100.0%\n", "    - Unique values: 1,461\n", "    - Data type: int64\n", "\n", "  claim_id:\n", "    - Completeness: 100.0%\n", "    - Unique values: 1,750\n", "    - Data type: int64\n", "\n", "  code_activity:\n", "    - Completeness: 100.0%\n", "    - Unique values: 769\n", "    - Data type: object\n", "\n", "  encounter_start_date:\n", "    - Completeness: 100.0%\n", "    - Unique values: 339\n", "    - Data type: object\n", "\n", "  type_activity:\n", "    - Completeness: 100.0%\n", "    - Unique values: 6\n", "    - Data type: int64\n", "\n", "  case_type:\n", "    - Completeness: 100.0%\n", "    - Unique values: 3\n", "    - Data type: object\n", "\n"]}], "source": ["# Data types and missing values analysis\n", "print(\"🔍 DATA TYPES AND COMPLETENESS ANALYSIS\")\n", "print(\"=\" * 60)\n", "\n", "# Create comprehensive data info\n", "data_info = pd.DataFrame({\n", "    'Column': df.columns,\n", "    'Data_Type': df.dtypes,\n", "    'Non_Null_Count': df.count(),\n", "    'Null_Count': df.isnull().sum(),\n", "    'Null_Percentage': (df.isnull().sum() / len(df) * 100).round(2),\n", "    'Unique_Values': [df[col].nunique() for col in df.columns]\n", "})\n", "\n", "# Display key fields analysis\n", "key_fields = ['aio_patient_id', 'case', 'claim_id', 'code_activity', \n", "              'encounter_start_date', 'type_activity', 'case_type']\n", "\n", "print(\"Key Fields Analysis:\")\n", "for field in key_fields:\n", "    if field in df.columns:\n", "        info = data_info[data_info['Column'] == field].iloc[0]\n", "        print(f\"  {field}:\")\n", "        print(f\"    - Completeness: {100 - info['Null_Percentage']:.1f}%\")\n", "        print(f\"    - Unique values: {info['Unique_Values']:,}\")\n", "        print(f\"    - Data type: {info['Data_Type']}\")\n", "        print()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Patient and Encounter Analysis"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["👥 PATIENT AND ENCOUNTER ANALYSIS\n", "============================================================\n", "📊 VOLUME METRICS:\n", "  Unique patients: 596\n", "  Unique encounters: 1,461\n", "  Unique claims: 1,750\n", "  Total activities: 4,999\n", "\n", "📈 RATIOS:\n", "  Activities per patient: 8.4\n", "  Activities per encounter: 3.4\n", "  Claims per encounter: 1.2\n", "  Encounters per patient: 2.5\n", "\n", "👥 PATIENT ACTIVITY DISTRIBUTION:\n", "  Min activities per patient: 1\n", "  Max activities per patient: 150\n", "  Mean activities per patient: 8.4\n", "  Median activities per patient: 3.0\n"]}], "source": ["# Patient and encounter statistics\n", "print(\"👥 PATIENT AND ENCOUNTER ANALYSIS\")\n", "print(\"=\" * 60)\n", "\n", "# Basic counts\n", "unique_patients = df['aio_patient_id'].nunique()\n", "unique_encounters = df['case'].nunique()\n", "unique_claims = df['claim_id'].nunique()\n", "total_activities = len(df)\n", "\n", "print(f\"📊 VOLUME METRICS:\")\n", "print(f\"  Unique patients: {unique_patients:,}\")\n", "print(f\"  Unique encounters: {unique_encounters:,}\")\n", "print(f\"  Unique claims: {unique_claims:,}\")\n", "print(f\"  Total activities: {total_activities:,}\")\n", "\n", "print(f\"\\n📈 RATIOS:\")\n", "print(f\"  Activities per patient: {total_activities / unique_patients:.1f}\")\n", "print(f\"  Activities per encounter: {total_activities / unique_encounters:.1f}\")\n", "print(f\"  Claims per encounter: {unique_claims / unique_encounters:.1f}\")\n", "print(f\"  Encounters per patient: {unique_encounters / unique_patients:.1f}\")\n", "\n", "# Patient activity distribution\n", "patient_activity = df.groupby('aio_patient_id').size()\n", "print(f\"\\n👥 PATIENT ACTIVITY DISTRIBUTION:\")\n", "print(f\"  Min activities per patient: {patient_activity.min()}\")\n", "print(f\"  Max activities per patient: {patient_activity.max()}\")\n", "print(f\"  Mean activities per patient: {patient_activity.mean():.1f}\")\n", "print(f\"  Median activities per patient: {patient_activity.median():.1f}\")"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1500x600 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["📊 ENCOUNTER SIZE ANALYSIS:\n", "  Single-activity encounters: 739 (50.6%)\n", "  Multi-activity encounters: 722 (49.4%)\n", "  Largest encounter: 99 activities\n"]}], "source": ["# Visualize patient activity distribution\n", "fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))\n", "\n", "# Patient activity histogram\n", "patient_activity.hist(bins=30, ax=ax1, alpha=0.7, color='skyblue')\n", "ax1.set_title('Distribution of Activities per Patient')\n", "ax1.set_xlabel('Number of Activities')\n", "ax1.set_ylabel('Number of Patients')\n", "ax1.axvline(patient_activity.mean(), color='red', linestyle='--', label=f'Mean: {patient_activity.mean():.1f}')\n", "ax1.legend()\n", "\n", "# Encounter size distribution\n", "encounter_size = df.groupby('case').size()\n", "encounter_size.hist(bins=20, ax=ax2, alpha=0.7, color='lightgreen')\n", "ax2.set_title('Distribution of Activities per Encounter')\n", "ax2.set_xlabel('Number of Activities')\n", "ax2.set_ylabel('Number of Encounters')\n", "ax2.axvline(encounter_size.mean(), color='red', linestyle='--', label=f'Mean: {encounter_size.mean():.1f}')\n", "ax2.legend()\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(f\"📊 ENCOUNTER SIZE ANALYSIS:\")\n", "print(f\"  Single-activity encounters: {(encounter_size == 1).sum():,} ({(encounter_size == 1).sum()/len(encounter_size)*100:.1f}%)\")\n", "print(f\"  Multi-activity encounters: {(encounter_size > 1).sum():,} ({(encounter_size > 1).sum()/len(encounter_size)*100:.1f}%)\")\n", "print(f\"  Largest encounter: {encounter_size.max()} activities\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Healthcare Encounter Types Analysis"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🏥 HEALTHCARE ENCOUNTER TYPES ANALYSIS\n", "============================================================\n", "📋 CASE TYPE DISTRIBUTION:\n", "  Outpatient Case: 4,117 (82.4%)\n", "  Day Patient Case: 791 (15.8%)\n", "  Inpatient Case: 91 (1.8%)\n"]}, {"data": {"image/png": "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***************************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", "text/plain": ["<Figure size 1500x600 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Case type analysis\n", "print(\"🏥 HEALTHCARE ENCOUNTER TYPES ANALYSIS\")\n", "print(\"=\" * 60)\n", "\n", "# Case type distribution\n", "case_types = df['case_type'].value_counts()\n", "print(\"📋 CASE TYPE DISTRIBUTION:\")\n", "for case_type, count in case_types.items():\n", "    percentage = (count / len(df)) * 100\n", "    print(f\"  {case_type}: {count:,} ({percentage:.1f}%)\")\n", "\n", "# Visualize case types\n", "fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))\n", "\n", "# Pie chart\n", "case_types.plot(kind='pie', ax=ax1, autopct='%1.1f%%', startangle=90)\n", "ax1.set_title('Distribution of Case Types')\n", "ax1.set_ylabel('')\n", "\n", "# Bar chart\n", "case_types.plot(kind='bar', ax=ax2, color=['skyblue', 'lightgreen', 'salmon'])\n", "ax2.set_title('Case Types - Volume')\n", "ax2.set_xlabel('Case Type')\n", "ax2.set_ylabel('Number of Activities')\n", "ax2.tick_params(axis='x', rotation=45)\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Medical Codes and Activity Types Analysis"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["💊 MEDICAL CODES AND ACTIVITY TYPES ANALYSIS\n", "============================================================\n", "📋 ACTIVITY TYPE DISTRIBUTION:\n", "  Type 3: CPT - 3,185 (63.7%)\n", "  Type 4: HCPCS - 512 (10.2%)\n", "  Type 5: Drug - 1,154 (23.1%)\n", "  Type 6: Dental - 78 (1.6%)\n", "  Type 8: Service Code - 69 (1.4%)\n", "  Type 9: IR-DRG - 1 (0.0%)\n", "\n", "🔍 MEDICAL CODES OVERVIEW:\n", "  Total unique codes: 769\n", "  Total activities: 4,999\n", "  Code reuse ratio: 6.5x\n", "  Average uses per code: 6.5\n"]}], "source": ["# Activity types analysis\n", "print(\"💊 MEDICAL CODES AND ACTIVITY TYPES ANALYSIS\")\n", "print(\"=\" * 60)\n", "\n", "# Activity type distribution\n", "activity_types = df['type_activity'].value_counts().sort_index()\n", "print(\"📋 ACTIVITY TYPE DISTRIBUTION:\")\n", "for activity_type, count in activity_types.items():\n", "    percentage = (count / len(df)) * 100\n", "    # Get description if available\n", "    desc = df[df['type_activity'] == activity_type]['act_type_desc'].iloc[0] if 'act_type_desc' in df.columns else 'N/A'\n", "    print(f\"  Type {activity_type}: {desc} - {count:,} ({percentage:.1f}%)\")\n", "\n", "# Unique codes analysis\n", "unique_codes = df['code_activity'].nunique()\n", "total_activities = len(df)\n", "print(f\"\\n🔍 MEDICAL CODES OVERVIEW:\")\n", "print(f\"  Total unique codes: {unique_codes:,}\")\n", "print(f\"  Total activities: {total_activities:,}\")\n", "print(f\"  Code reuse ratio: {total_activities/unique_codes:.1f}x\")\n", "print(f\"  Average uses per code: {total_activities/unique_codes:.1f}\")"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "🔍 CODE PATTERN ANALYSIS BY ACTIVITY TYPE:\n", "============================================================\n", "\n", "📋 CPT CODES (Type 3):\n", "  Records: 3,185 (63.7%)\n", "  Unique codes: 297\n", "  Standard CPT format (5 digits): 3,173 (99.6%)\n", "\n", "  Top 10 CPT codes:\n", "    99213: 462 times - Office or other outpatient visit for the evaluatio...\n", "    97110: 231 times - Therapeutic procedure, 1 or more areas,each 15 min...\n", "    97140: 225 times - Manual therapy techniques (eg, mobilization/ manip...\n", "    97014: 220 times - Application of a modality to 1 or more areas; elec...\n", "    99214: 195 times - Office or other outpatient visit for the evaluatio...\n", "    99203: 147 times - Office or other outpatient visit for the evaluatio...\n", "    99212: 114 times - Office or other outpatient visit for the evaluatio...\n", "    97010: 88 times - Application of a modality to 1 or more areas; hot ...\n", "    99204: 86 times - Office or other outpatient visit for the evaluatio...\n", "    96365: 58 times - Intravenous infusion, for therapy, prophylaxis, or...\n", "\n", "💊 DRUG CODES (Type 5):\n", "  Records: 1,154 (23.1%)\n", "  Unique codes: 428\n", "  UAE drug format: 1,138 (98.6%)\n", "\n", "  Top 10 drug codes:\n", "    H46-4867-05181-01: 72 times - SODIUM CHLORIDE 0.9%...\n", "    N01-1866-03129-01: 32 times - FEROSAC AMP 5's...\n", "    BQ4-9636-09864-01: 22 times - PARACETAMOL 10MG/ML 100ML VIA (PHARCO BINT'L)...\n", "    U00-7652-07538-01: 21 times - APOTEL MAX 1G/100ML BAG X 12'S...\n", "    H95-4003-04325-03: 19 times - PANALIFE 500MG TAB 24's...\n", "    O62-2809-00882-02: 17 times - LACTATED RINGER'S INJECTION USP XXII...\n"]}, {"ename": "TypeError", "evalue": "'float' object is not subscriptable", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mTypeError\u001b[0m                                 Traceback (most recent call last)", "Cell \u001b[0;32mIn[12], line 39\u001b[0m\n\u001b[1;32m     37\u001b[0m     \u001b[38;5;28;01mfor\u001b[39;00m code, count \u001b[38;5;129;01min\u001b[39;00m top_drugs\u001b[38;5;241m.\u001b[39mitems():\n\u001b[1;32m     38\u001b[0m         desc \u001b[38;5;241m=\u001b[39m drug_codes[drug_codes[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mcode_activity\u001b[39m\u001b[38;5;124m'\u001b[39m] \u001b[38;5;241m==\u001b[39m code][\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mactivity_desc\u001b[39m\u001b[38;5;124m'\u001b[39m]\u001b[38;5;241m.\u001b[39miloc[\u001b[38;5;241m0\u001b[39m]\n\u001b[0;32m---> 39\u001b[0m         \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m    \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mcode\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mcount\u001b[38;5;132;01m:\u001b[39;00m\u001b[38;5;124m,\u001b[39m\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m times - \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mdesc[:\u001b[38;5;241m50\u001b[39m]\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m...\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m     41\u001b[0m \u001b[38;5;66;03m# Dental codes (Type 6)\u001b[39;00m\n\u001b[1;32m     42\u001b[0m dental_codes \u001b[38;5;241m=\u001b[39m df[df[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mtype_activity\u001b[39m\u001b[38;5;124m'\u001b[39m] \u001b[38;5;241m==\u001b[39m \u001b[38;5;241m6\u001b[39m]\n", "\u001b[0;31mTypeError\u001b[0m: 'float' object is not subscriptable"]}], "source": ["# Analyze code patterns by activity type\n", "print(\"\\n🔍 CODE PATTERN ANALYSIS BY ACTIVITY TYPE:\")\n", "print(\"=\" * 60)\n", "\n", "# CPT codes (Type 3)\n", "cpt_codes = df[df['type_activity'] == 3]\n", "if len(cpt_codes) > 0:\n", "    print(f\"\\n📋 CPT CODES (Type 3):\")\n", "    print(f\"  Records: {len(cpt_codes):,} ({len(cpt_codes)/len(df)*100:.1f}%)\")\n", "    print(f\"  Unique codes: {cpt_codes['code_activity'].nunique():,}\")\n", "    \n", "    # Check for 5-digit numeric pattern (standard CPT)\n", "    cpt_pattern = cpt_codes['code_activity'].str.match(r'^\\d{5}$', na=False)\n", "    standard_cpt = cpt_pattern.sum()\n", "    print(f\"  Standard CPT format (5 digits): {standard_cpt:,} ({standard_cpt/len(cpt_codes)*100:.1f}%)\")\n", "    \n", "    print(\"\\n  Top 10 CPT codes:\")\n", "    top_cpt = cpt_codes['code_activity'].value_counts().head(10)\n", "    for code, count in top_cpt.items():\n", "        desc = cpt_codes[cpt_codes['code_activity'] == code]['activity_desc'].iloc[0]\n", "        print(f\"    {code}: {count:,} times - {desc[:50]}...\")\n", "\n", "# Drug codes (Type 5)\n", "drug_codes = df[df['type_activity'] == 5]\n", "if len(drug_codes) > 0:\n", "    print(f\"\\n💊 DRUG CODES (Type 5):\")\n", "    print(f\"  Records: {len(drug_codes):,} ({len(drug_codes)/len(df)*100:.1f}%)\")\n", "    print(f\"  Unique codes: {drug_codes['code_activity'].nunique():,}\")\n", "    \n", "    # Check for UAE drug code pattern\n", "    drug_pattern = drug_codes['code_activity'].str.contains(r'[A-Z]\\d+-\\d+-\\d+-\\d+', na=False)\n", "    uae_format = drug_pattern.sum()\n", "    print(f\"  UAE drug format: {uae_format:,} ({uae_format/len(drug_codes)*100:.1f}%)\")\n", "    \n", "    print(\"\\n  Top 10 drug codes:\")\n", "    top_drugs = drug_codes['code_activity'].value_counts().head(10)\n", "    for code, count in top_drugs.items():\n", "        desc = drug_codes[drug_codes['code_activity'] == code]['activity_desc'].iloc[0]\n", "        print(f\"    {code}: {count:,} times - {desc[:50]}...\")\n", "\n", "# Dental codes (Type 6)\n", "dental_codes = df[df['type_activity'] == 6]\n", "if len(dental_codes) > 0:\n", "    print(f\"\\n🦷 DENTAL CODES (Type 6):\")\n", "    print(f\"  Records: {len(dental_codes):,} ({len(dental_codes)/len(df)*100:.1f}%)\")\n", "    print(f\"  Unique codes: {dental_codes['code_activity'].nunique():,}\")\n", "    \n", "    print(\"\\n  Top 5 dental codes:\")\n", "    top_dental = dental_codes['code_activity'].value_counts().head(5)\n", "    for code, count in top_dental.items():\n", "        desc = dental_codes[dental_codes['code_activity'] == code]['activity_desc'].iloc[0]\n", "        print(f\"    {code}: {count:,} times - {desc[:50]}...\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Financial and Claims Status Analysis"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["💰 FINANCIAL AND CLAIMS STATUS ANALYSIS\n", "============================================================\n", "📋 CLAIM STATUS DISTRIBUTION:\n", "  Fully Paid: 3,286 (65.7%)\n", "  Partially Rejected: 1,583 (31.7%)\n", "  Fully Rejected: 124 (2.5%)\n", "  Not Remitted: 6 (0.1%)\n", "\n", "💰 FINANCIAL METRICS:\n", "  Gross: Total $705,006.09, Mean $141.03\n", "  Patient Share: Total $44,716.77, Mean $8.95\n", "  Net: Total $660,289.32, Mean $132.08\n", "  Payment Amount: Total $545,183.74, Mean $109.06\n", "  Rejected Amount: Total $114,946.58, Mean $22.99\n", "\n", "❌ DENIAL CODES (Top 10):\n", "  PRCE-001: 441 (8.8%)\n", "  MNEC-004: 197 (3.9%)\n", "  NCOV-001: 40 (0.8%)\n", "  MNEC-005: 36 (0.7%)\n", "  CLAI-012: 31 (0.6%)\n", "  PRCE-002: 19 (0.4%)\n", "  CODE-014: 17 (0.3%)\n", "  NCOV-003: 16 (0.3%)\n", "  COPY-001: 9 (0.2%)\n", "  CLAI-017: 8 (0.2%)\n"]}], "source": ["# Claims status analysis\n", "print(\"💰 FINANCIAL AND CLAIMS STATUS ANALYSIS\")\n", "print(\"=\" * 60)\n", "\n", "# Claim mapping status\n", "claim_status = df['claim_mapping_status'].value_counts()\n", "print(\"📋 CLAIM STATUS DISTRIBUTION:\")\n", "for status, count in claim_status.items():\n", "    percentage = (count / len(df)) * 100\n", "    print(f\"  {status}: {count:,} ({percentage:.1f}%)\")\n", "\n", "# Financial analysis\n", "print(f\"\\n💰 FINANCIAL METRICS:\")\n", "financial_cols = ['gross', 'patient_share', 'net', 'payment_amount', 'rejected_amount']\n", "for col in financial_cols:\n", "    if col in df.columns:\n", "        total = df[col].sum()\n", "        mean_val = df[col].mean()\n", "        print(f\"  {col.replace('_', ' ').title()}: Total ${total:,.2f}, Mean ${mean_val:.2f}\")\n", "\n", "# Denial codes analysis\n", "denial_codes = df['denial_code'].value_counts().dropna()\n", "if len(denial_codes) > 0:\n", "    print(f\"\\n❌ DENIAL CODES (Top 10):\")\n", "    for code, count in denial_codes.head(10).items():\n", "        percentage = (count / len(df)) * 100\n", "        print(f\"  {code}: {count:,} ({percentage:.1f}%)\")"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"image/png": "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********************************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", "text/plain": ["<Figure size 1600x1200 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Visualize financial and status data\n", "fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))\n", "\n", "# Claim status pie chart\n", "claim_status.plot(kind='pie', ax=ax1, autopct='%1.1f%%', startangle=90)\n", "ax1.set_title('Claim Status Distribution')\n", "ax1.set_ylabel('')\n", "\n", "# Financial amounts distribution\n", "financial_data = df[['gross', 'net', 'payment_amount']].sum()\n", "financial_data.plot(kind='bar', ax=ax2, color=['lightblue', 'lightgreen', 'salmon'])\n", "ax2.set_title('Total Financial Amounts')\n", "ax2.set_ylabel('Amount ($)')\n", "ax2.tick_params(axis='x', rotation=45)\n", "\n", "# Payment amount distribution\n", "df['payment_amount'].hist(bins=50, ax=ax3, alpha=0.7, color='lightgreen')\n", "ax3.set_title('Distribution of Payment Amounts')\n", "ax3.set_xlabel('Payment Amount ($)')\n", "ax3.set_ylabel('Frequency')\n", "\n", "# Rejection rate by activity type\n", "rejection_by_type = df.groupby('type_activity')['claim_mapping_status'].apply(\n", "    lambda x: (x.str.contains('Rejected', na=False)).mean() * 100\n", ")\n", "rejection_by_type.plot(kind='bar', ax=ax4, color='salmon')\n", "ax4.set_title('Rejection Rate by Activity Type')\n", "ax4.set_xlabel('Activity Type')\n", "ax4.set_ylabel('Rejection Rate (%)')\n", "ax4.tick_params(axis='x', rotation=0)\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. Payer and Insurance Analysis"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🏢 PAYER AND INSURANCE ANALYSIS\n", "============================================================\n", "📋 PAYER DISTRIBUTION:\n", "  Daman Insurance: 3,076 (61.5%)\n", "  Abu Dhabi Insurance Company: 1,889 (37.8%)\n", "  Al Hilal Takaful - PSC: 28 (0.6%)\n", "  Al Sagr Insurance Company: 4 (0.1%)\n", "  THIQA: 2 (0.0%)\n", "\n", "📋 TOP 10 INSURANCE PLANS:\n", "  Silver: 1,505 (30.1%)\n", "  COMPREHENSIVE 3 - ALDAR: 1,276 (25.5%)\n", "  BASIC: 612 (12.2%)\n", "  Comprehensive 3: 367 (7.3%)\n", "  Basic: 273 (5.5%)\n", "  Platinum: 230 (4.6%)\n", "  Essential 5: 140 (2.8%)\n", "  Gold: 95 (1.9%)\n", "  Exclusive 1 Prime: 78 (1.6%)\n", "  EXCLUSIVE1PRIME–ALDAR EX USCAN: 61 (1.2%)\n", "\n", "🌐 TOP 5 NETWORKS:\n", "  SILVER: 1,500 (30.0%)\n", "  ALDAR-COMP 3: 1,179 (23.6%)\n", "  Basic: 660 (13.2%)\n", "  LOW END: 243 (4.9%)\n", "  PLATINUM: 226 (4.5%)\n"]}], "source": ["# Payer analysis\n", "print(\"🏢 PAYER AND INSURANCE ANALYSIS\")\n", "print(\"=\" * 60)\n", "\n", "# Payer distribution\n", "payers = df['payer_id_desc'].value_counts()\n", "print(\"📋 PAYER DISTRIBUTION:\")\n", "for payer, count in payers.items():\n", "    percentage = (count / len(df)) * 100\n", "    print(f\"  {payer}: {count:,} ({percentage:.1f}%)\")\n", "\n", "# Insurance plan analysis\n", "plans = df['plan_name'].value_counts().head(10)\n", "print(f\"\\n📋 TOP 10 INSURANCE PLANS:\")\n", "for plan, count in plans.items():\n", "    percentage = (count / len(df)) * 100\n", "    print(f\"  {plan}: {count:,} ({percentage:.1f}%)\")\n", "\n", "# Network analysis\n", "networks = df['network_name'].value_counts().head(5)\n", "print(f\"\\n🌐 TOP 5 NETWORKS:\")\n", "for network, count in networks.items():\n", "    percentage = (count / len(df)) * 100\n", "    print(f\"  {network}: {count:,} ({percentage:.1f}%)\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8. Provider and Clinician Analysis"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["👨‍⚕️ PROVIDER AND CLINICIAN ANALYSIS\n", "============================================================\n", "📋 PROVIDER OVERVIEW:\n", "  Unique providers: 10\n", "  Most active provider: MF4252 (2,934 activities)\n", "\n", "🏥 INSTITUTION ANALYSIS:\n", "  BDSC: 2,934 (58.7%)\n", "  BURJEEL-AL AIN: 1,075 (21.5%)\n", "  BURJEEL-AD: 586 (11.7%)\n", "  BURJEEL ASHAREJ: 176 (3.5%)\n", "  BMC-SHAMKHA: 162 (3.2%)\n", "  LLH MC -MS: 28 (0.6%)\n", "  LLH OASIS: 16 (0.3%)\n", "  BURJEEL-SHARJAH: 11 (0.2%)\n", "  LLH-MS: 8 (0.2%)\n", "  BMC-BARARI: 3 (0.1%)\n", "\n", "👨‍⚕️ TOP 10 CLINICIANS BY ACTIVITY:\n", "  <PERSON><PERSON><PERSON> (GD10670): 269 (5.4%)\n", "  MOUSTAPHA AWADA (GD21895): 256 (5.1%)\n", "  <PERSON><PERSON> (GD5891): 250 (5.0%)\n", "  <PERSON><PERSON><PERSON> (GD20346): 179 (3.6%)\n", "  <PERSON><PERSON> (GD25091): 156 (3.1%)\n", "  MOUNIR HAIDER HAIDER . (GD8357): 134 (2.7%)\n", "  Tawfek Burhan Tawfek Al- Abed (GD37430): 102 (2.0%)\n", "  <PERSON><PERSON> (GD25783): 95 (1.9%)\n", "  QUSAI MUSTAFA ALYAFAWEE (GD24090): 94 (1.9%)\n", "  <PERSON><PERSON> (GD20029): 93 (1.9%)\n"]}], "source": ["# Provider analysis\n", "print(\"👨‍⚕️ PROVIDER AND CLINICIAN ANALYSIS\")\n", "print(\"=\" * 60)\n", "\n", "# Provider distribution\n", "providers = df['provider_id'].value_counts()\n", "print(f\"📋 PROVIDER OVERVIEW:\")\n", "print(f\"  Unique providers: {df['provider_id'].nunique()}\")\n", "print(f\"  Most active provider: {providers.index[0]} ({providers.iloc[0]:,} activities)\")\n", "\n", "# Institution analysis\n", "institutions = df['institution_name'].value_counts()\n", "print(f\"\\n🏥 INSTITUTION ANALYSIS:\")\n", "for institution, count in institutions.items():\n", "    percentage = (count / len(df)) * 100\n", "    print(f\"  {institution}: {count:,} ({percentage:.1f}%)\")\n", "\n", "# Clinician analysis\n", "clinicians = df['clinician_name'].value_counts().head(10)\n", "print(f\"\\n👨‍⚕️ TOP 10 CLINICIANS BY ACTIVITY:\")\n", "for clinician, count in clinicians.items():\n", "    percentage = (count / len(df)) * 100\n", "    clinician_id = df[df['clinician_name'] == clinician]['clinician'].iloc[0]\n", "    print(f\"  {clinician} ({clinician_id}): {count:,} ({percentage:.1f}%)\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 9. Temporal Analysis"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📅 TEMPORAL ANALYSIS\n", "============================================================\n", "📅 DATE RANGE:\n", "  Start date range: 2023-01-01 00:00:00 to 2023-12-31 00:00:00\n", "  End date range: 2023-01-01 00:00:00 to 2023-12-31 00:00:00\n", "  Total days covered: 364 days\n", "\n", "📊 MONTHLY ACTIVITY:\n", "  2023-01: 321 activities\n", "  2023-02: 328 activities\n", "  2023-03: 206 activities\n", "  2023-04: 129 activities\n", "  2023-05: 129 activities\n", "  2023-06: 1,353 activities\n", "  2023-07: 237 activities\n", "  2023-08: 247 activities\n", "  2023-09: 466 activities\n", "  2023-10: 617 activities\n", "  2023-11: 605 activities\n", "  2023-12: 361 activities\n", "\n", "⏱️ ENCOUNTER DURATION:\n", "  Same-day encounters: 4,908 (98.2%)\n", "  Multi-day encounters: 91 (1.8%)\n", "  Average duration: 0.1 days\n", "  Max duration: 4 days\n"]}], "source": ["# Temporal analysis\n", "print(\"📅 TEMPORAL ANALYSIS\")\n", "print(\"=\" * 60)\n", "\n", "# Convert dates\n", "df['encounter_start_date'] = pd.to_datetime(df['encounter_start_date'], format='%d/%m/%Y')\n", "df['encounter_end_date'] = pd.to_datetime(df['encounter_end_date'], format='%d/%m/%Y')\n", "\n", "# Date range analysis\n", "print(f\"📅 DATE RANGE:\")\n", "print(f\"  Start date range: {df['encounter_start_date'].min()} to {df['encounter_start_date'].max()}\")\n", "print(f\"  End date range: {df['encounter_end_date'].min()} to {df['encounter_end_date'].max()}\")\n", "print(f\"  Total days covered: {(df['encounter_start_date'].max() - df['encounter_start_date'].min()).days} days\")\n", "\n", "# Monthly activity analysis\n", "df['month'] = df['encounter_start_date'].dt.to_period('M')\n", "monthly_activity = df['month'].value_counts().sort_index()\n", "\n", "print(f\"\\n📊 MONTHLY ACTIVITY:\")\n", "for month, count in monthly_activity.items():\n", "    print(f\"  {month}: {count:,} activities\")\n", "\n", "# Encounter duration analysis\n", "df['encounter_duration'] = (df['encounter_end_date'] - df['encounter_start_date']).dt.days\n", "print(f\"\\n⏱️ ENCOUNTER DURATION:\")\n", "print(f\"  Same-day encounters: {(df['encounter_duration'] == 0).sum():,} ({(df['encounter_duration'] == 0).sum()/len(df)*100:.1f}%)\")\n", "print(f\"  Multi-day encounters: {(df['encounter_duration'] > 0).sum():,} ({(df['encounter_duration'] > 0).sum()/len(df)*100:.1f}%)\")\n", "print(f\"  Average duration: {df['encounter_duration'].mean():.1f} days\")\n", "print(f\"  Max duration: {df['encounter_duration'].max()} days\")"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAABdEAAAPdCAYAAABlRyFLAAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjAsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvlHJYcgAAAAlwSFlzAAAPYQAAD2EBqD+naQABAABJREFUeJzs3Xl8VPW9//H3OWeSACELQZIQ9n1LSNjE4AIKggt1aa1aW+vW6r22KoKt9fa2FW+va3/qdav11mprW+ltFVs3FDcQCTsJ+74vISwhCRCSzDnf3x8DAzEsCUxyJsnr+Xjw6Fm+58x7pqgzn/nO52sZY4wAAAAAAAAAAEANtt8BAAAAAAAAAACIVhTRAQAAAAAAAAA4CYroAAAAAAAAAACcBEV0AAAAAAAAAABOgiI6AAAAAAAAAAAnQREdAAAAAAAAAICToIgOAAAAAAAAAMBJUEQHAAAAAAAAAOAkKKIDAAAAAAAAAHASFNEBAAAQNV5//XVZliXLsvTFF1/UOG+MUc+ePWVZlkaNGlWvWWbPnq2HH35Y+/fvr3Gua9euGj9+/Gnv8cUXX5z0uZyt5557TpZlKTMz84zvsWPHDj388MPKz8+vce7hhx+WZVlndN+uXbvq1ltvrdXjnKmjf09O96c+Xvu6OJvXEQAAANEh4HcAAAAA4OsSEhL06quv1iiUz5gxQ+vXr1dCQkK9Z5g9e7YmT56sW2+9VcnJyfX+eHX1hz/8QZK0fPlyzZ07V8OHD6/zPXbs2KHJkyera9euysnJqXbuBz/4gS677LIzyjZ16lQlJibW6nHOVF5eXrX9//qv/9Lnn3+uzz77rNrx/v37R+TxAAAA0HxRRAcAAEDUueGGG/SXv/xFL774YrVi7Kuvvqrc3FyVlpb6mM5/CxYsUEFBga688kq9//77evXVV8+oiH4qHTt2VMeOHc/o2kGDBkU0y4mcd9551fbbtWsn27ZrHP+6Q4cOqVWrVvUZDQAAAE0M7VwAAAAQdb7zne9Ikt58883wsZKSEr311lu6/fbbT3jNvn37dPfdd6tDhw6KjY1V9+7d9fOf/1wVFRXVxlmWpR//+Md644031K9fP7Vq1UrZ2dl67733wmMefvhh/eQnP5EkdevW7aStQaZNm6bBgwerZcuW6tu3b3h2+Mm88cYbsiyrxixqSXrkkUcUExOjHTt2nPIeUujLBEl6/PHHNWLECE2ZMkWHDh2qMW779u2688471alTJ8XGxiojI0PXXXeddu3apS+++ELDhg2TJN12223h5/jwww+HX4Pj25Bcc8016tKlizzPq/E4w4cP1+DBg8P7x7dzOdXjROr1OJlRo0YpMzNTM2fO1IgRI9SqVavw35/S0lI98MAD6tatm2JjY9WhQwdNmDBBBw8erHaP2vx9Oer9999XTk6O4uLi1K1bN/3mN7854+wAAACIHhTRAQAAEHUSExN13XXXVStKv/nmm7JtWzfccEON8YcPH9bFF1+sP/3pT5o4caLef/99fe9739OTTz6pb37zmzXGv//++3rhhRf0yCOP6K233lJKSoquvfZabdiwQVKolck999wjSXr77beVl5envLy8aoXigoICTZo0Sffff7/++c9/auDAgbrjjjs0c+bMkz6vG264Qenp6XrxxRerHQ8Gg/rd736na6+9VhkZGad8bcrLy/Xmm29q2LBhyszM1O23366ysjL9/e9/rzZu+/btGjZsmKZOnaqJEyfqww8/1LPPPqukpCQVFxdr8ODBeu211yRJ//mf/xl+jj/4wQ9O+Li33367tmzZUqNdyqpVqzRv3jzddtttJ7zuVI8TidfjdHbu3Knvfe97uummm/TBBx/o7rvv1qFDhzRy5Ej98Y9/1L333qsPP/xQDz74oF5//XVdddVVMsZUu8fp/r5I0qeffqqrr75aCQkJmjJlip566in93//9X/i5AwAAoBEzAAAAQJR47bXXjCQzf/588/nnnxtJZtmyZcYYY4YNG2ZuvfVWY4wxAwYMMCNHjgxf9/LLLxtJ5v/+7/+q3e+JJ54wkszHH38cPibJpKWlmdLS0vCxwsJCY9u2eeyxx8LHnnrqKSPJbNy4sUbOLl26mBYtWpjNmzeHj5WXl5uUlBRz1113hY8dfQ6ff/55+NivfvUrExsba3bt2hU+9re//c1IMjNmzDjta/SnP/3JSDIvv/yyMcaYsrIy07p1a3PhhRdWG3f77bebmJgYs2LFipPea/78+UaSee2112qc+9WvfmWO/7hQVVVl0tLSzE033VRt3E9/+lMTGxtr9uzZEz7WpUsXc8stt9T6cc7m9TjqlltuMfHx8dWOjRw50kgyn376abXjjz32mLFt28yfP7/a8X/84x9Gkvnggw/Cx2r792X48OEmIyPDlJeXh4+VlpaalJQUw8cuAACAxo2Z6AAAAIhKI0eOVI8ePfSHP/xBS5cu1fz580/ayuWzzz5TfHy8rrvuumrHj7YU+fTTT6sdv/jii6stTpqWlqbU1FRt3ry51vlycnLUuXPn8H6LFi3Uu3fv097j3//93yVJ//u//xs+9sILLygrK0sXXXTRaR/31VdfVcuWLXXjjTdKklq3bq1vf/vb+vLLL7V27drwuA8//FAXX3yx+vXrV+vndCqBQEDf+9739Pbbb6ukpESS5Lqu3njjDV199dVq27btGd33bF+P02nTpo0uueSSasfee+89ZWZmKicnR8FgMPxn3LhxJ2zbc7q/LwcPHtT8+fP1zW9+Uy1atAiPS0hI0De+8Y2zfg4AAADwF0V0AAAARCXLsnTbbbfpz3/+s15++WX17t1bF1544QnH7t27V+np6dV6eEtSamqqAoGA9u7dW+34iQq+cXFxKi8vr3W+M71HWlqabrjhBv3ud7+T67pasmSJvvzyS/34xz8+7WOuW7dOM2fO1JVXXiljjPbv36/9+/eHvzw4vv3N7t27z3hh0JO5/fbbdfjwYU2ZMkWS9NFHH2nnzp0nbeVSG2fzetRG+/btaxzbtWuXlixZopiYmGp/EhISZIzRnj17qo0/3f/XxcXF8jxP6enpNcad6BgAAAAaF4roAAAAiFq33nqr9uzZo5dffvmUhdq2bdtq165dNXpZFxUVKRgM6pxzzqnvqHVy3333aevWrfrnP/+pF154QcnJyfrud7972uv+8Ic/yBijf/zjH2rTpk34z5VXXilJ+uMf/yjXdSVJ7dq107Zt2yKau3///jr33HPDfb5fe+01ZWRkaOzYsWd13zN9PWrj61+sSNI555yjrKwszZ8//4R/fvGLX9TpMdq0aSPLslRYWFjj3ImOAQAAoHGhiA4AAICo1aFDB/3kJz/RN77xDd1yyy0nHTd69GgdOHBA77zzTrXjf/rTn8Ln6youLk6S6jQ7vbaGDBmiESNG6IknntBf/vIX3XrrrYqPjz/lNa7r6o9//KN69Oihzz//vMafSZMmaefOnfrwww8lSZdffrk+//xzrV69+qT3PJPneNttt2nu3LmaNWuW3n33Xd1yyy1yHOeU15zucc7k9Tgb48eP1/r169W2bVsNHTq0xp+uXbvW6X7x8fE699xz9fbbb+vw4cPh42VlZXr33XcjnB4AAAANLeB3AAAAAOBUHn/88dOO+f73v68XX3xRt9xyizZt2qSsrCzNmjVLjz76qK644gqNGTOmzo+blZUlSfqf//kf3XLLLYqJiVGfPn2q9cY+G/fdd59uuOEGWZalu++++7TjP/zwQ+3YsUNPPPGERo0aVeN8ZmamXnjhBb366qsaP368HnnkEX344Ye66KKL9B//8R/KysrS/v37NW3aNE2cOFF9+/ZVjx491LJlS/3lL39Rv3791Lp1a2VkZCgjI+OkOb7zne9o4sSJ+s53vqOKiopw3/lTqc3j1PX1OBsTJkzQW2+9pYsuukj333+/Bg4cKM/ztGXLFn388ceaNGmShg8fXqd7/td//Zcuu+wyXXrppZo0aZJc19UTTzyh+Ph47du3r56eCQAAABoCM9EBAADQ6LVo0UKff/65vvvd7+qpp57S5Zdfrtdff10PPPCA3n777TO656hRo/TQQw/p3Xff1QUXXKBhw4Zp4cKFEct8zTXXKC4uTuPGjVOvXr1OO/7VV19VbGzsSdvanHPOObr22mv13nvvadeuXerQoYPmzZun8ePH6/HHH9dll12me+65RyUlJUpJSZEktWrVSn/4wx+0d+9ejR07VsOGDdMrr7xyyhxJSUm69tprtW3bNp1//vnq3bv3abPX5nHq+nqcjfj4eH355Ze69dZb9corr+jKK6/U9ddfr+eee04dO3as80x0Sbr00kv1zjvvqLS0VDfccIMmTpyob33rWyddDBcAAACNh2W+3jgSAAAAQL179913ddVVV+n999/XFVdc4Xcc3/F6AAAAIFpRRAcAAAAa0IoVK7R582bdd999io+P16JFi064+GVzwesBAACAaEc7FwAAAKAB3X333brqqqvUpk0bvfnmm82+YMzrAQAAgGjHTHQAAAAAAAAAAE6CmegAAAAAAAAAAJwERXQAAAAAAAAAAE4i4HeAxsLzPO3YsUMJCQn0aQQAAAAAAACAKGKMUVlZmTIyMmTbkZ07ThG9lnbs2KFOnTr5HQMAAAAAAAAAcBJbt25Vx44dI3pPX4voM2fO1FNPPaWFCxdq586dmjp1qq655poTjr3rrrv0yiuv6JlnntGECRPCxysqKvTAAw/ozTffVHl5uUaPHq2XXnqp2gtVXFyse++9V//6178kSVdddZWef/55JScn1zprQkKCJGnz5s11ug4AAAAAAAAAUL9KS0vVqVOncB03knwtoh88eFDZ2dm67bbb9K1vfeuk49555x3NnTtXGRkZNc5NmDBB7777rqZMmaK2bdtq0qRJGj9+vBYuXCjHcSRJN910k7Zt26Zp06ZJku68807dfPPNevfdd2ud9WgLl8TERCUmJtblaQIAAAAAAAAAGkB9tOL2tYh++eWX6/LLLz/lmO3bt+vHP/6xPvroI1155ZXVzpWUlOjVV1/VG2+8oTFjxkiS/vznP6tTp0765JNPNG7cOK1cuVLTpk3TnDlzNHz4cEnS//7v/yo3N1erV69Wnz59Tvi4FRUVqqioCO+XlpZKCvVG9zzvjJ8zAAAAAAAAACCy6rNmG9U90T3P080336yf/OQnGjBgQI3zCxcuVFVVlcaOHRs+lpGRoczMTM2ePVvjxo1TXl6ekpKSwgV0STrvvPOUlJSk2bNnn7SI/thjj2ny5Mk1ju/evVuVlZUReHYAAAAAAAAAgEgoKyurt3tHdRH9iSeeUCAQ0L333nvC84WFhYqNjVWbNm2qHU9LS1NhYWF4TGpqao1rU1NTw2NO5KGHHtLEiRPD+0d76rRr146e6AAAAAAAAAAQRVq0aFFv947aIvrChQv1P//zP1q0aFGd+9gYY6pdc6Lrvz7m6+Li4hQXF1fjuG3bsm27TnkAAAAAAAAAAPWnPmu2UVsN/vLLL1VUVKTOnTsrEAgoEAho8+bNmjRpkrp27SpJSk9PV2VlpYqLi6tdW1RUpLS0tPCYXbt21bj/7t27w2MAAAAAAAAAADiRqC2i33zzzVqyZIny8/PDfzIyMvSTn/xEH330kSRpyJAhiomJ0fTp08PX7dy5U8uWLdOIESMkSbm5uSopKdG8efPCY+bOnauSkpLwGAAAAAAAAAAATsTXdi4HDhzQunXrwvsbN25Ufn6+UlJS1LlzZ7Vt27ba+JiYGKWnp4cXA01KStIdd9yhSZMmqW3btkpJSdEDDzygrKwsjRkzRpLUr18/XXbZZfrhD3+o3/3ud5KkO++8U+PHjz/poqIAAAAAAAAAAEg+F9EXLFigiy++OLx/dCHPW265Ra+//nqt7vHMM88oEAjo+uuvV3l5uUaPHq3XX39djuOEx/zlL3/Rvffeq7Fjx0qSrrrqKr3wwguReyIAAAAAAAAAgCbJMsYYv0M0BqWlpUpKSlJxcbGSk5P9jgMAAAAAAAAAOOJo/bakpESJiYkRvXfU9kQHAAAAAAAAAMBvvrZzAQAAAPxgPE/ehm1S6QEpsbXs7h1l2cwvAQAAAFATRXQAAAA0K+6SNaqa+qlUUnbsYFKCYq4dLWdgb/+CAQAAAIhKTLcBAABAs+EuWaOq19+pXkCXpJIyVb3+jtwla3zJBQAAACB6UUQHAABAs2A8LzQD/RSq3vlUxvMaKBEAAACAxoAiOgAAAJoFb8O2mjPQv25/WWgcAAAAABxBER0AAADNQ+mByI4DAAAA0CxQRAcAAEDzkNg6suMAAAAANAsU0QEAANAs2N07SkkJpx6UnBAaBwAAAABHUEQHAABAs2DZtmKuHX3KMTHXjJZl8xYZAAAAwDF8QgAAAECzYWf2kuJbnvhkfEvZmT0bNhAAAACAqEcRHQAAAM2G2bBVOlguSbI6pSvme+NldWofOnmwXN7qTf6FAwAAABCVKKIDAACg2XAXLA9vB0YOlTO4vwKXnnfsfF6+D6kAAAAARDOK6AAAAGgWTGWV3ILVoZ242FBrF0l2vx5SUmtJkrdivcz+Mr8iAgAAAIhCFNEBAADQLHjL1koVlZIkJ7uPrNgYSZLl2HKGDzwyyMidu8SviAAAAACiEEV0AAAANAvHt3Jxhg6odi4wfKBkWZKk4NwlMp7XoNkAAAAARC+K6AAAAGjyTOmBY4uGtkmU1b1TtfNWm0TZ/bqHdvaXyVu5oWEDAgAAAIhaFNEBAADQ5LmLVkjGSJKcIQNk2VaNMU5u9rHxeQUNlg0AAABAdKOIDgAAgCbPnX98K5f+Jxxj9+suJSdIkryVG2SKSxskGwAAAIDoRhEdAAAATZq3vUhm525JktW5vezUticcZ9l2qDe6JBmjIAuMAgAAABBFdAAAADRx7oJl4W1nWOYpxzrDB0pHWr24c5bIuCwwCgAAADR3FNEBAADQZBnXk7toZWjHseXk9D3leCs5QXb/HqGd0gPyVq6v54QAAAAAoh1FdAAAADRZ3ppNUtlBSZLdv4es+JanvcbJzQlvu7NZYBQAAABo7iiiAwAAoMlyFxy/oOipW7kcZffpKrVJlCR5qzfI21dSH9EAAAAANBIU0QEAANAkmcMV8pauDe20aiG7X/daXWfZtgLnZR+5Sag3OgAAAIDmiyI6AAAAmiS3YLUUDEqSnEH9ZAWcWl/rDM+S7NBbZXfuEhnXrZeMAAAAAKIfRXQAAAA0SWfSyuUoK7G17AE9QztlB+UtZ4FRAAAAoLmiiA4AAIAmx9tXIrN+qyTJSk2R1Tm9zvdwRmSHt928/EhFAwAAANDIUEQHAABAk+MtPG4W+pABsiyrzvewe3WV1TY5dL/Vm+Tt3R+hdAAAAAAaE4roAAAAaFKMMdVbuQzpf0b3sWxLznkDw/tuXsFZZwMAAADQ+FBEBwAAQJNiNu+U2V0sSbJ7dJKVknTG93LOPW6B0XlLZYIsMAoAAAA0NxTRAQAA0KS4x7VysYfVbUHRr7MS4mVn9QrtHDgkb9nas7ofAAAAgMaHIjoAAACaDBMMyl28MrQTE5AzsPdZ39PJzQlv09IFAAAAaH4oogMAAKDJ8FZskA4dliTZWb1ktYg763vaPTvLOic5dP+1m+UdaRUDAAAAoHmgiA4AAIAmo9qCokPPrpXLUaEFRrOPPcYcZqMDAAAAzQlFdAAAADQJ5sAheSvXh3YS42X36hKxezvnZkmOI+noAqPBiN0bAAAAQHSjiA4AAIAmwc1fJbmeJMkZ3F+WE7m3ulbrVrIHHllg9GC5vKUsMAoAAAA0FxTRAQAA0CTURyuX41VbYHR2fsTvDwAAACA6UUQHAABAo+ft2iuzZackyeqQKjujXcQfw+7RSVZqSujx1m+VV7Q34o8BAAAAIPpQRAcAAECj5y48bhb6kAH18hiW9bUFRvNYYBQAAABoDiiiAwAAoFEznjnWysWy5AzuV2+P5QzLlAJHFhidv0ymigVGAQAAgKaOIjoAAAAaNW/9Fml/mSTJ7tNNVmLrenssK76l7IF9QjuHDstbsrreHgsAAABAdKCIDgAAgEbNW7givO0Mq59WLscLjDjW0iVISxcAAACgyaOIDgAAgEbLVFbJLVgV2mkRK3tAz3p/TKtbR1lpbUOPv2GbvMI99f6YAAAAAPxDER0AAACNlrd0rVRRJUlysvvIio2p98e0LEtOLguMAgAAAM2Fr0X0mTNn6hvf+IYyMjJkWZbeeeed8Lmqqio9+OCDysrKUnx8vDIyMvT9739fO3bsqHaPiooK3XPPPTrnnHMUHx+vq666Stu2bas2pri4WDfffLOSkpKUlJSkm2++Wfv372+AZwgAAID6FF5QVJIzNLPBHtcZmikFAkcyLJOprGqwxwYAAADQsHwtoh88eFDZ2dl64YUXapw7dOiQFi1apF/84hdatGiR3n77ba1Zs0ZXXXVVtXETJkzQ1KlTNWXKFM2aNUsHDhzQ+PHj5bpueMxNN92k/Px8TZs2TdOmTVN+fr5uvvnmen9+AAAAqD+mpEzemk2SJCslSVa3jg322FarFrJzjiwwWl4hr4AFRgEAAICmyjLGGL9DSKGfxU6dOlXXXHPNScfMnz9f5557rjZv3qzOnTurpKRE7dq10xtvvKEbbrhBkrRjxw516tRJH3zwgcaNG6eVK1eqf//+mjNnjoYPHy5JmjNnjnJzc7Vq1Sr16dOnVvlKS0uVlJSk4uJiJScnn+3TBQAAwFkKfj5PwXe/kCQ5l+Yq5vILG/TxvY3bVfn8XyRJVtcOirv3uw36+AAAAACOOVq/LSkpUWJiYkTvHYjo3epZSUmJLMsKF7EXLlyoqqoqjR07NjwmIyNDmZmZmj17tsaNG6e8vDwlJSWFC+iSdN555ykpKUmzZ88+aRG9oqJCFRUV4f3S0lJJkud58jyvHp4dAAAAassYI3f+svC+Pbh/g79HM53TZaWfI1O4R2bTdgW375Ldvl2DZgAAAAAQUp+fBxpNEf3w4cP62c9+pptuuin8TUJhYaFiY2PVpk2bamPT0tJUWFgYHpOamlrjfqmpqeExJ/LYY49p8uTJNY7v3r1blZWVZ/NUAAAAcJacXfuUULhHkhTMaKf9pkoqKmrwHLGZ3dXqSI6Dn89R+Zjhp7kCAAAAQH0oKyurt3s3iiJ6VVWVbrzxRnmep5deeum0440xsiwrvH/89snGfN1DDz2kiRMnhvdLS0vVqVMntWvXjnYuAAAAPgvOWa6j80zizstWqxNMmmgIZmSiqmYskqqCiluxSa2vu0xWbIwvWQAAAIDmrEWLFvV276gvoldVVen666/Xxo0b9dlnn1XrZ5Oenq7KykoVFxdXm41eVFSkESNGhMfs2rWrxn13796ttLS0kz5uXFyc4uLiahy3bVu27et6rAAAAM2acT15i1eFdhxbgUH9ZPn1/iy+lbxB/eTOWyodrpBZskbOuVn+ZAEAAACasfqs2UZ1NfhoAX3t2rX65JNP1LZt22rnhwwZopiYGE2fPj18bOfOnVq2bFm4iJ6bm6uSkhLNmzcvPGbu3LkqKSkJjwEAAEDj4a3ZJJUdlCTZ/XvKim/pax4nNzu87c7O9y8IAAAAgHrh60z0AwcOaN26deH9jRs3Kj8/XykpKcrIyNB1112nRYsW6b333pPruuEe5ikpKYqNjVVSUpLuuOMOTZo0SW3btlVKSooeeOABZWVlacyYMZKkfv366bLLLtMPf/hD/e53v5Mk3XnnnRo/fvxJFxUFAABA9HIXHFtQ1Bk2wMckIVbn9rIyUmV2FMls2Slve5HsDv60lwEAAAAQeb7ORF+wYIEGDRqkQYMGSZImTpyoQYMG6Ze//KW2bdumf/3rX9q2bZtycnLUvn378J/Zs2eH7/HMM8/ommuu0fXXX6/zzz9frVq10rvvvivHccJj/vKXvygrK0tjx47V2LFjNXDgQL3xxhsN/nwBAABwdkx5hbylRyZhxLeU3be7v4EUWn+n2mz0vHz/wgAAAACIOMsYY/wO0RiUlpYqKSlJxcXFLCwKAADgk+CcJQr+3zRJknPBYMV8c4zPiULM4QpVPPySVFklxcUq7uG7ZcXF+h0LAAAAaDaO1m9LSkqqrasZCVHdEx0AAAA4XrVWLkP9b+VylNUiTs6gfqGdikq5i1f6GwgAAABAxFBEBwAAQKPg7d0vs2GbJMlKTZHVKd3nRNU5I45v6VLgYxIAAAAAkUQRHQAAAI2Ct3BFeNsZminLsnxMU5Pdqb2sjmmSJLO1UN62Qp8TAQAAAIgEiugAAACIesaYY61cLMkZ0t/fQCdRbYHR2cxGBwAAAJoCiugAAACIembzDpk9+yVJdo/OstpEdqGgSHEG9ZPiYiRJ7uIVMocrfE4EAAAA4GxRRAcAAEDUcxcsD2/bUbSg6NdZLeLkDD4yS76iSu4iFhgFAAAAGjuK6AAAAIhqJhiUu3hVaCcmIGdgb38DnYaTmxPedvPyZYzxLwwAAACAs0YRHQAAAFHNW7FBKj8sSbKzestqEedzolOzO6bJ6pQuSTLbi2S2ssAoAAAA0JhRRAcAAEBUCy8oKskZFr2tXI739dnoAAAAABoviugAAACIWubAodBMdElKjJfdq4u/gWrJGdRXahErSXIXr5IpZ4FRAAAAoLGiiA4AAICo5S5eJXmeJMkZMkCW3TjevlpxsXKGHJk1X1kld9EKfwMBAAAAOGON41MIAAAAmqVqrVyGNo5WLkc5udnhbXc2C4wCAAAAjRVFdAAAAEQlb9fe8KKcVodU2e3b+ZyobuyMVFldMiRJZudumc07fU4EAAAA4ExQRAcAAEBUchcsD287QzN9THLmqs1GZ4FRAAAAoFGiiA4AAICoYzwjd+GRIrptyRncz99AZ8jJ6Su1iJMkufmrZMoP+5wIAAAAQF1RRAcAAEDU8dZvkfaXSZLsPt1kJcT7nOjMWLExx3q5VwWrza4HAAAA0DhQRAcAAEDUqd7KpXEtKPp1zoic8LabV8ACowAAAEAjQxEdAAAAUcVUVMpbsjq00yJW9oCe/gY6S3b6ObK6dZAkmcI9Mpu2+5wIAAAAQF1QRAcAAEBU8ZatlSqqJElOdl9ZsTE+Jzp7gdyc8HYwr8C/IAAAAADqjCI6AAAAokq1Vi7DGncrl6Psgb2lli0kSV7+KpmD5T4nAgAAAFBbFNEBAAAQNcz+MnlrNkuSrJQkWV07+pwoMqzYmGNfCARdFhgFAAAAGhGK6AAAAIga7qIV0pGFN+2hA2TZls+JIsc5rqWLm5fPAqMAAABAI0ERHQAAAFHBGFO9lcuQptHK5Sg7ra2s7qGZ9aZon8yGbT4nAgAAAFAbFNEBAAAQFcz2IpnCPZIkq2sH2e3a+Jwo8qovMJrvWw4AAAAAtUcRHQAAAFHBXbAsvO0MbVqz0I+ys3tL8S0lSV7BGpkDh3xOBAAAAOB0KKIDAADAd8Z15S5aGdpxHDk5ff0NVE+sQEDOsMzQjutW++IAAAAAQHSiiA4AAADfeas3SUdmZdsDeshq1cLfQPXIOS87vO3mFbDAKAAAABDlKKIDAADAd82hlctRdmqK7J6dJUlmd7G8dVt8TgQAAADgVCiiAwAAwFem/LC8ZetCO/EtZfft7m+gBuDkVp+NDgAAACB6UUQHAACAr9yC1VLQlSQ5g/rJCjg+J6p/dlZvqXUrSZK3dI1M2UGfEwEAAAA4GYroAAAA8JW7YHl42xnWtFu5HGUFnOMWGPXkzmeBUQAAACBaUUQHAACAb7y9+2U2bJMkWWltZXVM9zlRw6mxwKjHAqMAAABANKKIDgAAAN94x89CHzpAlmX5mKZh2e3ayO7dRZJk9u6Xt26zz4kAAAAAnAhFdAAAAPjCGHOslYslOYP7+xvIB1+fjQ4AAAAg+lBEBwAAgC/Mph0ye/dLkuyeXWS1SfQ3kA/szF7HLTC6Vqb0gM+JAAAAAHwdRXQAAAD4wl1wbDFNZ2jzWFD066yAI+fcrNCO58mdxwKjAAAAQLShiA4AAIAGZ6qCcvNXhXZiY2QP7O1vIB85uce1dJnDAqMAAABAtKGIDgAAgAbnrVgvlVdIkuysXrLiYn1O5B+7bbLsPl0lSWZfibw1m3zNAwAAAKA6iugAAABocOEFRSU5QzN9TBIdnNyc8Labl+9bDgAAAAA1UUQHAABAgzIHDslbuSG0k9hadq/O/gaKAvaAHlJCvCTJW76OBUYBAACAKEIRHQAAAA3KXbxS8jxJkjOkvyybt6SW48gZPjC04xm5c5f6GwgAAABAGJ9YAAAA0KCqt3IZ4GOS6BI4b6BkhbaDcwpkjnzRAAAAAMBfFNEBAADQYLzCPTJbCyVJVsc02e3b+ZwoelgpSbL7dA/tFJfKW73J1zwAAAAAQiiiAwAAoMEwC/3UnBHZ4W0WGAUAAACiA0V0AAAANAjjeXIXrQjt2JacQf38DRSF7H49pKTWkiRvxXqZ/WU+JwIAAABAER0AAAANwlu3VTpSFLb7dpeVEO9zouhjOfbXFhhd4m8gAAAAAP4W0WfOnKlvfOMbysjIkGVZeuedd6qdN8bo4YcfVkZGhlq2bKlRo0Zp+fLl1cZUVFTonnvu0TnnnKP4+HhdddVV2rZtW7UxxcXFuvnmm5WUlKSkpCTdfPPN2r9/fz0/OwAAABzPXbAsvE0rl5MLDB8oWaEVRoNzl7DAKAAAAOAzX4voBw8eVHZ2tl544YUTnn/yySf19NNP64UXXtD8+fOVnp6uSy+9VGVlx37WOmHCBE2dOlVTpkzRrFmzdODAAY0fP16u64bH3HTTTcrPz9e0adM0bdo05efn6+abb6735wcAAIAQU1Epb8ma0E6LONkDevobKIpZbRJl9zuywOj+MnkrN/gbCAAAAGjmLGOM8TuEJFmWpalTp+qaa66RFJqFnpGRoQkTJujBBx+UFJp1npaWpieeeEJ33XWXSkpK1K5dO73xxhu64YYbJEk7duxQp06d9MEHH2jcuHFauXKl+vfvrzlz5mj48OGSpDlz5ig3N1erVq1Snz59TpinoqJCFRUV4f3S0lJ16tRJe/fuVXJycv29EAAAAE2Qu3C53Dc/lCTZwwcq8O2xPieKbt6K9Qr+YaokyerXXTF3fNPnRAAAAEB0Ky0tVZs2bVRSUqLExMSI3jsQ0btF0MaNG1VYWKixY499wIqLi9PIkSM1e/Zs3XXXXVq4cKGqqqqqjcnIyFBmZqZmz56tcePGKS8vT0lJSeECuiSdd955SkpK0uzZs09aRH/sscc0efLkGsd3796tysrKCD5TAACApi8+L18xR7ZLerSXW1Tka56olxKvxIRWsssOyVu1UbvXbZRJpIc8AAAAcDLHdy+JtKgtohcWFkqS0tLSqh1PS0vT5s2bw2NiY2PVpk2bGmOOXl9YWKjU1NQa909NTQ2POZGHHnpIEydODO8fnYnerl07ZqIDAADUgSkpU9XmnaGdlCSlDMqUdaTnN07Ozc2R+/FsWcaozfodCow73+9IAAAAQNRq0aJFvd07aovoR339A5Yx5rQfur4+5kTjT3efuLg4xcXF1Thu27Zs29dW8gAAAI1KcPEq6UgDQWfoADmO42+gRsI6L1vuJ3mSZ+TNXSpr7PmyHN6HAgAAACdSnzXbqH0Xnp6eLkk1ZosXFRWFZ6enp6ersrJSxcXFpxyza9euGvffvXt3jVnuAAAAiCxjjNz5y8L7ztABPqZpXKzkBNn9e4R2Sg/IW7ne30AAAABAMxW1RfRu3bopPT1d06dPDx+rrKzUjBkzNGLECEnSkCFDFBMTU23Mzp07tWzZsvCY3NxclZSUaN68eeExc+fOVUlJSXgMAAAA6ofZvktm115JktWtg+xz2pzmChzPyc0Jb7uzC/wLAgAAADRjdW7nsmjRIsXExCgrK0uS9M9//lOvvfaa+vfvr4cfflixsbG1vteBAwe0bt268P7GjRuVn5+vlJQUde7cWRMmTNCjjz6qXr16qVevXnr00UfVqlUr3XTTTZKkpKQk3XHHHZo0aZLatm2rlJQUPfDAA8rKytKYMWMkSf369dNll12mH/7wh/rd734nSbrzzjs1fvz4ky4qCgAAgMhw5y8PbzMLve7sPl2lNolScam81Rvk7SuRnZLkdywAAACgWanzTPS77rpLa9askSRt2LBBN954o1q1aqW///3v+ulPf1qney1YsECDBg3SoEGDJEkTJ07UoEGD9Mtf/lKS9NOf/lQTJkzQ3XffraFDh2r79u36+OOPlZCQEL7HM888o2uuuUbXX3+9zj//fLVq1UrvvvtutV6bf/nLX5SVlaWxY8dq7NixGjhwoN544426PnUAAADUgXFduYtXhnYCjpzsvv4GaoQs21bgvOzQjpHcOUv8DQQAAAA0Q5YxxtTlgqSkJC1atEg9evTQE088oc8++0wfffSRvvrqK914443aunVrfWX1VWlpqZKSklRcXKzk5GS/4wAAAEQ9d/k6Vb36tiTJzu6j2Fuu9jlR42RKD6jikZclz5MS4hX3y3+TxeKsAAAAQDVH67clJSVKTEyM6L3rPBPdGCPP8yRJn3zyia644gpJUqdOnbRnz56IhgMAAEDj5S6glUskWImtZQ/oGdopOyhvOQuMAgAAAA2pzkX0oUOH6te//rXeeOMNzZgxQ1deeaWkUD/ztLS0iAcEAABA42PKD8tbfmTtm9atZPft5m+gRs4ZkR3edvPy/QsCAAAANEN1LqI/++yzWrRokX784x/r5z//uXr2DM2K+cc//qERI0ZEPCAAAAAaHzd/tRR0JUnOoH60HzlLdq+ustomS5K81Zvk7d3vax4AAACgOQnU9YKBAwdq6dKlNY4/9dRT1RbzBAAAQPNFK5fIsmxLznkDFXx/piTJzSuQPX6kz6kAAACA5qHOM9Elaf/+/fr973+vhx56SPv27ZMkrVixQkVFRRENBwAAgMbH21Mss3GbJMlKayurIy3/IsE5N0uyQ2/f3XlLZY7M9AcAAABQv+pcRF+yZIl69eqlJ554Qr/5zW+0f/9+SdLUqVP10EMPRTofAAAAGhlv4YrwtjM0U5Zl+Zim6bAS4mVn9QrtHDgkb9lafwMBAAAAzUSdi+gTJ07UbbfdprVr16pFixbh45dffrlmzpwZ0XAAAABoXIwxx1q5WJIzpL+/gZoYJzcnvO3mFfgXBAAAAGhG6lxEnz9/vu66664axzt06KDCwsKIhAIAAEDjZDZtlzmy6KXdq4us5AR/AzUxds/Oss5JliR5azfL213sbyAAAACgGahzEb1FixYqLS2tcXz16tVq165dREIBAACgcXLnH7+gaKaPSZqm0AKj2eF9dw6z0QEAAID6Vuci+tVXX61HHnlEVVVVkiTLsrRlyxb97Gc/07e+9a2IBwQAAEDjYKqCcvNXhXZiY47170ZEOedmSY4j6egCo0GfEwEAAABNW52L6L/5zW+0e/dupaamqry8XCNHjlTPnj2VkJCg//7v/66PjAAAAGgEvOXrpMMVkiR7YG9ZcbE+J2qarNatZA888gXFwXJ5S1lgFAAAAKhPgbpekJiYqFmzZumzzz7TokWL5HmeBg8erDFjxtRHPgAAADQS4QVFRSuX+ubk5shbHJr1787OlzOon8+JAAAAgKarzkX0oy655BJdcsklkcwCAACARsqUHZS3akNoJ6m17J6d/A3UxNk9OslKTZEp2idv/VZ5RXtlp7b1OxYAAADQJNWqiP7cc8/pzjvvVIsWLfTcc8+dcuy9994bkWAAAABoPNzFqyTPSJKcIQNk2XXuGog6sKzQAqPBf30uSXLzCmRfzQQXAAAAoD5YxhhzukHdunXTggUL1LZtW3Xr1u3kN7MsbdiwIaIBo0VpaamSkpJUXFys5ORkv+MAAABElYqn/yizbZckKfant8tOP8fnRE2fOViuiskvSUFXatVCcb+6W1bMGf/QFAAAAGjUjtZvS0pKlJiYGNF71+pd9saNG0+4DQAAAHiFe8IFdKtjGgX0BmLFt5Q9sI+8RSukQ4flLVktZ8gAv2MBAAAATU6df2f7yCOP6NChQzWOl5eX65FHHolIKAAAADQeLCjqn8CI7PB2MK/AxyQAAABA01XnIvrkyZN14MCBGscPHTqkyZMnRyQUAAAAGgfjeXIXHimi27acQX39DdTMWN06ykoLLShqNmyTV7jH50QAAABA01PnIroxRpZl1TheUFCglJSUiIQCAABA4+Ct2yKVhCZY2P26yUqI9zlR82JZlpzcY7PRXWajAwAAABFX65WH2rRpI8uyZFmWevfuXa2Q7rquDhw4oH/7t3+rl5AAAACITrRy8Z8zNFPB92ZKwaDcBcsUuPIiWbExfscCAAAAmoxaF9GfffZZGWN0++23a/LkyUpKSgqfi42NVdeuXZWbm1svIQEAABB9TEWlvCVrQjst42T37+FvoGbKatVCdk4feQuWS+UV8gpWyxnGFxoAAABApNS6iH7LLbdIkrp166YRI0YoJobZLQAAAM2Zt2SNVFklSXJy+sqKqfVbS0RYIDdHlUd+FRDMK6CIDgAAAERQrT7plJaWKjExUZI0aNAglZeXq7y8/IRjj44DAABA00Yrl+hhdc2QlX6OTOEemU3b5e3cLbt9O79jAQAAAE1CrRYWbdOmjYqKiiRJycnJatOmTY0/R48DAACg6TP7y+St2yxJstomy+qa4XOi5i20wGhOeJ8FRgEAAIDIqdVM9M8++0wpKSnh7eMXFQUAAEDz4y5cIZnQtjN0AO8Po4AztL+C730hVQXlLliuwPiRLDAKAAAARECtiugjR44Mb48aNaq+sgAAAKARMMbIXbAsvG8PHeBjGhxltWwhZ1A/ufOWSocr5OavUuDcLL9jAQAAAI1erdq5HK979+76xS9+odWrV9dHHgAAAEQ5s22XzK69kiSrW0fZbZP9DYQwJzc7vO3OzvcvCAAAANCE1LmI/uMf/1jTpk1Tv379NGTIED377LPauXNnfWQDAABAFKq+oCiz0KOJ1bm9rIxUSZLZslPe9iKfEwEAAACNX52L6BMnTtT8+fO1atUqjR8/Xr/97W/VuXNnjR07Vn/605/qIyMAAACihHFduYtWhHYCjpycPv4GQjWhBUaPm42el+9fGAAAAKCJqHMR/ajevXtr8uTJWr16tb788kvt3r1bt912WySzAQAAIMp4qzZKB8slSXZmT1ktW/icCF/nDOkvHVlQ1F24Qqai0udEAAAAQON2xkV0SZo3b54mTJiga6+9VqtXr9Z1110XqVwAAACIQtVbuWT6mAQnY7WIkzOoX2inolLu4pX+BgIAAAAauToX0desWaNf/epX6tWrl84//3ytWLFCjz/+uHbt2qW//e1v9ZERAAAAUcAcOixv2brQTutWsvt09TUPTs4ZcXxLlwIfkwAAAACNX6CuF/Tt21dDhw7Vj370I914441KT0+vj1wAAACIMm7+Ksl1JUnO4H6yHMfnRDgZu1N7WR3TZLbtktlaKG9boeyOvG8HAAAAzkSdi+irVq1S79696yMLAAAAohitXBoXJzdbwb9/LElyZxfIvp4iOgAAAHAm6tzOhQI6AABA8+PtKZbZtF2SZKWfI6tDqs+JcDrOoH5S3JEFRhevkDlc4XMiAAAAoHGqVRE9JSVFe/bskSS1adNGKSkpJ/0DAACApqf6LPQBsizLxzSoDatFnJzB/UM7FVVyF7HAKAAAAHAmatXO5ZlnnlFCQkJ4mw9NAAAAzYcxRt7RIrqlY4VZRD0nNye8sKibly8nN5v38gAAAEAd1aqIfsstt4S3b7311vrKAgAAgChkNm6X2VciSbJ7dZWVnOBzItSW3TFNVqd0ma2FMtuLZLYWyurc3u9YAAAAQKNS557ojuOoqKioxvG9e/fKcZyIhAIAAED0cBcsC287Qwf4mARnwsnNCW+7efm+5QAAAAAaqzoX0Y0xJzxeUVGh2NjYsw4EAACA6GEqq+Tmrw7txMbIzurlbyDUmTOor9Qi9D7dXbxKppwFRgEAAIC6qFU7F0l67rnnJEmWZen3v/+9WrduHT7nuq5mzpypvn37Rj4hAAAAfOOtWC8dDhVd7ew+suKYNNHYWHGxcoYMkPvVYqmySu6iFQqcP8jvWAAAAECjUesi+jPPPCMpNBP95Zdfrta6JTY2Vl27dtXLL78c+YQAAADwDa1cmgYnNztURJfkzs6XMyKHBUYBAACAWqp1EX3jxo2SpIsvvlhvv/222rRpU2+hAAAA4D9TdlDeqtB7QCUnyO7R2d9AOGN2RqqsLhkym3fI7Nwts3mnrK4ZfscCAAAAGoU690T//PPPKaADAAA0A+6ilZIXWg/HGTJAls3M5cbMyc0Ob7PAKAAAAFB7dS6iX3fddXr88cdrHH/qqaf07W9/OyKhAAAA4D934fLwNq1cGj8np6/UIk6S5Oavkik/7HMiAAAAoHGocxF9xowZuvLKK2scv+yyyzRz5syIhDoqGAzqP//zP9WtWze1bNlS3bt31yOPPCLP88JjjDF6+OGHlZGRoZYtW2rUqFFavnx5tftUVFTonnvu0TnnnKP4+HhdddVV2rZtW0SzAgAANCXezt0y23ZJkqxO6bLT2vqcCGfLio059mVIVVDuguWnvgAAAACApDMooh84cECxsbE1jsfExKi0tDQioY564okn9PLLL+uFF17QypUr9eSTT+qpp57S888/Hx7z5JNP6umnn9YLL7yg+fPnKz09XZdeeqnKysrCYyZMmKCpU6dqypQpmjVrlg4cOKDx48fLdd2I5gUAAGgqji+wMgu96XBG5IS33bwCGWP8CwMAAAA0ErVeWPSozMxM/e1vf9Mvf/nLasenTJmi/v37RyyYJOXl5enqq68Oz3zv2rWr3nzzTS1YsEBSaBb6s88+q5///Of65je/KUn64x//qLS0NP31r3/VXXfdpZKSEr366qt64403NGbMGEnSn//8Z3Xq1EmffPKJxo0bd8LHrqioUEVFRXj/6BcEnudVmwkPAADQ1BjPk7twRWjHtmVl9+H9T1ORmiKraweZTdtlCvfI3bBNdrcOfqcCAAAAzlp9fmapcxH9F7/4hb71rW9p/fr1uuSSSyRJn376qd588039/e9/j2i4Cy64QC+//LLWrFmj3r17q6CgQLNmzdKzzz4rSdq4caMKCws1duzY8DVxcXEaOXKkZs+erbvuuksLFy5UVVVVtTEZGRnKzMzU7NmzT1pEf+yxxzR58uQax3fv3q3KysqIPk8AAIBoEti0Q61LD0iSqrpnaP+hA9KhAz6nQqTEDOiq+E3bJUmHvpirQ/EX+JwIAAAAOHvHdyaJtDoX0a+66iq98847evTRR/WPf/xDLVu21MCBA/XJJ59o5MiREQ334IMPqqSkRH379pXjOHJdV//93/+t73znO5KkwsJCSVJaWlq169LS0rR58+bwmNjYWLVp06bGmKPXn8hDDz2kiRMnhvdLS0vVqVMntWvXTsnJyZF4egAAAFEp+MkCHZ3D0XLEYMWnpvqaB5Fl2rRR1WcLpfLDil29WfE3XC6rVUu/YwEAAABnpUWLFvV27zoX0SXpyiuvPOHiovn5+crJyTnbTGF/+9vf9Oc//1l//etfNWDAAOXn52vChAnKyMjQLbfcEh5nWVa164wxNY593enGxMXFKS4ursZx27Zl23VuJQ8AANAomMMV8patDe20jJOT2VMW732alrg4ecMGyJ25UAq6MgtXyhk51O9UAAAAwFmpz5rtWd+5pKREL730kgYPHqwhQ4ZEIlPYT37yE/3sZz/TjTfeqKysLN188826//779dhjj0mS0tPTJanGjPKioqLw7PT09HRVVlaquLj4pGMAAAAQ4i1dK1VWSZKcQf1kBc5ozgWinJObE9528/JZYBQAAAA4hTMuon/22Wf67ne/q/bt2+v555/XFVdcEV7wM1IOHTpU4xsEx3HCTeK7deum9PR0TZ8+PXy+srJSM2bM0IgRIyRJQ4YMUUxMTLUxO3fu1LJly8JjAAAAEOIuWBbedoYO8DEJ6pOd1lZW946SJFO0T2bDNp8TAQAAANGrTlOLtm3bptdff11/+MMfdPDgQV1//fWqqqrSW2+9pf79+0c83De+8Q3993//tzp37qwBAwZo8eLFevrpp3X77bdLCrVxmTBhgh599FH16tVLvXr10qOPPqpWrVrppptukiQlJSXpjjvu0KRJk9S2bVulpKTogQceUFZWlsaMGRPxzAAAAI2VKS6Vt26LJMk6J1lWlwyfE6E+BXJzVHWkeB7My1dsj04+JwIAAACiU62L6FdccYVmzZql8ePH6/nnn9dll10mx3H08ssv11u4559/Xr/4xS909913q6ioSBkZGbrrrrv0y1/+Mjzmpz/9qcrLy3X33XeruLhYw4cP18cff6yEhITwmGeeeUaBQEDXX3+9ysvLNXr0aL3++utyHKfesgMAADQ27sIV0pGuHs7QzNOuMYPGzc7uLb3TUjpYLq9gjcw1h2S1buV3LAAAACDqWKaWDRADgYDuvfde/fu//7t69eoVPh4TE6OCgoJ6mYkeTUpLS5WUlKTi4mIlJyf7HQcAACCijDGqfPIPMrv2SpJif36n7LbJ/oZCvav61+dyv5gvSQpcNUqBUef6nAgAAAA4M0frtyUlJUpMTIzovWvdE/3LL79UWVmZhg4dquHDh+uFF17Q7t27IxoGAAAA/jDbCsMFdKt7RwrozYRzXnZ4280rYIFRAAAA4ARqXUTPzc3V//7v/2rnzp266667NGXKFHXo0EGe52n69OkqKyurz5wAAACoR+785eFtFhRtPuzUFNk9O0uSzO7icE98AAAAAMfUuoh+VKtWrXT77bdr1qxZWrp0qSZNmqTHH39cqampuuqqq+ojIwAAAOqRCbpyF68M7QQcOdl9/Q2EBuXkVp+NDgAAAKC6OhfRj9enTx89+eST2rZtm958881IZQIAAEAD8lZtkA6WS5LszF6yWsb5nAgNyc7qLR1ZUNRbukam7KDPiQAAAIDoclZF9KMcx9E111yjf/3rX5G4HQAAABqQu4BWLs2ZFXDkDMsM7bie3PnL/A0EAAAARJmIFNEBAADQOJmD5fKWrw/ttG4lu083fwPBFzUWGPVYYBQAAAA4iiI6AABAM+YWrJJcV5LkDOkvy+HtYXNkt2sju3cXSZLZu1/eus0+JwIAAACiB5+SAAAAmjFaueAoJzcnvM0CowAAAMAxtSqiDx48WMXFxZKkRx55RIcOHarXUAAAAKh/3u59Mpt2SJKs9u1kZaT6nAh+sjN7SgnxkiRv6VqZ0gM+JwIAAACiQ62K6CtXrtTBgwclSZMnT9aBA7yhBgAAaOzchSvC287QAbIsy8c08JvlOHLOzQrteJ7ceSwwCgAAAEhSoDaDcnJydNttt+mCCy6QMUa/+c1v1Lp16xOO/eUvfxnRgAAAAIg84xl5R1u5WJacwf38DYSo4Jw3UO5ncyQjuXMK5FwyXJbNlysAAABo3mpVRH/99df1q1/9Su+9954sy9KHH36oQKDmpZZlUUQHAABoBMymbTL7SiRJdu8uspISfE6EaGC3TZbdu5u81Rtl9pXIW7NJTt9ufscCAAAAfFWrInqfPn00ZcoUSZJt2/r000+VmkrPTAAAgMbKnc+CojgxJzdb3uqNkiQ3L58iOgAAAJq9WhXRj+d5Xn3kAAAAQAMxlVVyC1aFduJiZGf28jcQooo9oIeUGC+VHpS3fJ1MSRm/VAAAAECzVquFRb9u/fr1uueeezRmzBhdeumluvfee7V+/fpIZwMAAEA98Javkw5XSpKcgX1kxcX6nAjRxHIcOcMHhnY8I3feUn8DAQAAAD6rcxH9o48+Uv/+/TVv3jwNHDhQmZmZmjt3rgYMGKDp06fXR0YAAABEkLvgWCsXm1YuOIHA8IHSkfVEg3OWyPBrVAAAADRjdW7n8rOf/Uz333+/Hn/88RrHH3zwQV166aURCwcAAIDIMqUHwv2ulZwgu0dnfwMhKlkpSbL7dpe3coNUXCpv1UY5/Xv4HQsAAADwRZ1noq9cuVJ33HFHjeO33367VqxYEZFQAAAAqB/u4pWSZySFFhS1bMvnRIhWTm52eNvNK/AxCQAAAOCvOhfR27Vrp/z8/BrH8/PzlZqaGolMAAAAqCfHt3JxhtDKBSdn9+shJbWWJHkr1svsL/M5EQAAAOCPOrdz+eEPf6g777xTGzZs0IgRI2RZlmbNmqUnnnhCkyZNqo+MAAAAiABvx26Z7UWSJKtze9lpbX1OhGhmObac4QPlfjxbMkbu3CUKjDvf71gAAABAg6tzEf0Xv/iFEhIS9P/+3//TQw89JEnKyMjQww8/rHvvvTfiAQEAABAZ7sLjZqGzoChqIXBettzpeZIxCs5dImdMriynzj9mBQAAABq1OhfRLcvS/fffr/vvv19lZaGfdCYkJEQ8GAAAACLHeN6xIrpty8np628gNApWcoLs/t3lLV8v7S+Tt2qDnAE9/Y4FAAAANKizmkaSkJBAAR0AAKAR8NZulkoPSpLs/t1ltW7lcyI0Fk5uTnjbzcv3LQcAAADgF36LCQAA0Ay4849v5ZLpYxI0NnbfblKbREmSt3KjTHGpz4kAAACAhkURHQAAoIkzhyvkLV0T2mnZQnb/7v4GQqNi2bYCwweGdoxRcE6Bv4EAAACABkYRHQAAoIlzl6yRqoKSJGdQX1mBOi+Lg2bOGZ4l2ZYkyZ27VMb1fE4EAAAANJw6FdGrqqp08cUXa82aNfWVBwAAABHmLaCVC86OlZQgu/+RBUVLD8hbsd7fQAAAAEADqlMRPSYmRsuWLZNlWfWVBwAAABFk9pXIW7dFkmS1ayOrS3ufE6GxcnKzw9ssMAoAAIDmpM7tXL7//e/r1VdfrY8sAAAAiDB30YrwtjN0AJMhcMbsPl2PLTC6eqO8fSX+BgIAAAAaSJ0bYlZWVur3v/+9pk+frqFDhyo+Pr7a+aeffjpi4QAAAHDmjDFyj2vlYg8Z4GMaNHaWbStwXraCH34pGcmdUyD7iov8jgUAAADUuzoX0ZctW6bBgwdLUo3e6MxsAgAAiB5ma6FM0T5JktWjk+yUJJ8TobFzhmcp+NFXkufJnbtUgXHny3Icv2MBAAAA9arORfTPP/+8PnIAAAAgwtxqC4oyCx1nz0psLXtAT3lL10hlB+UtWycnu4/fsQAAAIB6Veee6EetW7dOH330kcrLyyWFfi4MAACA6GCCrtzFK0M7gQCFTkSMM+L4BUYLfEwCAAAANIw6F9H37t2r0aNHq3fv3rriiiu0c+dOSdIPfvADTZo0KeIBAQAAUHfeqg3SwdBkBzurp6wWcT4nQlNh9+oqq22yJMlbs0nenmJ/AwEAAAD1rM5F9Pvvv18xMTHasmWLWrVqFT5+ww03aNq0aRENBwAAgDPjzj++lUumj0nQ1Fi2Jee8geF9d84SH9MAAAAA9a/ORfSPP/5YTzzxhDp27FjteK9evbR58+aIBQMAAMCZMQfL5a1YF9pJiJfdu6uvedD0OOdmSXboo4Q7b6lM0PU5EQAAAFB/6lxEP3jwYLUZ6Eft2bNHcXH8TBgAAMBvbv4qyfUkSc7gfrKcM14GBzghKyFedlav0M6BQ/KWrfU3EAAAAFCP6vyJ6qKLLtKf/vSn8L5lWfI8T0899ZQuvvjiiIYDAABA3bkLaOWC+ufk5oS33bx833IAAAAA9S1Q1wueeuopjRo1SgsWLFBlZaV++tOfavny5dq3b5+++uqr+sgIAACAWvKK9sls3iFJstq3k90h1edEaKrsnp1lnZMss2e/vLVb5O3eJ7tdit+xAAAAgIir80z0/v37a8mSJTr33HN16aWX6uDBg/rmN7+pxYsXq0ePHvWREQAAALXkLjxuFvqwAT4mQVMXWmA0O7zv5hX4mAYAgOqM58ldt0XuohVy122R8Ty/IwFoxOo8E12S0tPTNXny5EhnAQAAwFkwnpG3cEVox7LkDO7vbyA0ec65WQp+OEtyXbnzlylwxYWyAmf0EQMAgIhxl6xR1dRPpZKyYweTEhRz7Wg5A3v7FwxAo3VG73CLi4v16quvauXKlbIsS/369dNtt92mlBR+vgkAAOAXs3GbzL4SSZLdp6usxNY+J0JTZ7VuJXtgL3mLV0kHy+UtWcOXNwAAX7lL1qjq9XdqnigpCx2/9RoK6QDqrM7tXGbMmKFu3brpueeeU3Fxsfbt26fnnntO3bp104wZM+ojIwAAAGrBXbAsvO0MoZULGsbxC4wGaekCAPCR8bzQDPRTqPr7R3LXbpa3eYe8bbvkFe6Rt6dYZn+ZzIFDMocrZKqCMsY0UGpEEm18UF/qPBP9Rz/6ka6//nr99re/leM4kiTXdXX33XfrRz/6kZYtW3aaOwAAACDSTGWV3PzVoZ24GNlZvfwNhGbD7tFJVmqKTNE+mfVb5e3aKzutrd+xAADNkJu/qnoLlxM5WK6q3/6tdjd0bCngSIGA5DiyAk5o33HCx2seOzouED4eHnOiex13rRUISAFbcgLH3efoNXbo8Zw6z4dtNmjjg/pU5yL6+vXr9dZbb4UL6JLkOI4mTpyoP/3pTxENBwAAgNrxlq+TKiolSU52X1mxMT4nQnNhWaEFRoP/+lyS5M4pkH31JT6nAgA0F96+Enn5q+QWrJbZWhjZm7te6E9FlSTpRHPTG3y+umV9rSBvVy/aBxxZTl2K9tWP17jX1740qHn/I9dZVkO/EtXQxgf1rc5F9MGDB2vlypXq06dPteMrV65UTk5OpHKFbd++XQ8++KA+/PBDlZeXq3fv3nr11Vc1ZMgQSZIxRpMnT9Yrr7yi4uJiDR8+XC+++KIGDDj2E+aKigo98MADevPNN1VeXq7Ro0frpZdeUseOHSOeFwAAwA/u/OXhbXsorVzQsJxhmQp+MFMKHl1g9CJZMSwwCgCoH6a4VG7BKrn5q2W27Kzz9XZOX1kJ8ZLrSkFXJhiUgqFtua7M0e3jjhvXPTYm6Epu0IcKuiRjpKpg6I8qQoe+PqTBQ6naTPzqRfuTFfePzOT/2rHQfuAk9zr+i4NjY4xtqeqt6aeMV/XOp7Ize8qymcmPM1Ord7ZLliwJb99777267777tG7dOp133nmSpDlz5ujFF1/U448/HtFwxcXFOv/883XxxRfrww8/VGpqqtavX6/k5OTwmCeffFJPP/20Xn/9dfXu3Vu//vWvdemll2r16tVKSEiQJE2YMEHvvvuupkyZorZt22rSpEkaP368Fi5cWG1GPQAAQGNkSg/IW70xtNMmUXb3Tv4GQrNjxbeUPbCPvEUrpEOH5RWslsOXOQCACDLFpXKXrA4VzjfvOPGgDqnS3v3S4cqT3yg5QTHfG3/WxVRjjOR5Jyi+n6wgf+ycOXIuXKA/es3xx9zT38scdy583K8e4O6RHBVHXp8TDPG1y/z+Mnkbtsnp2dnPFGjELFOLlRJs25ZlWaddVMGyLLmuG7FwP/vZz/TVV1/pyy+/POF5Y4wyMjI0YcIEPfjgg5JCs87T0tL0xBNP6K677lJJSYnatWunN954QzfccIMkaceOHerUqZM++OADjRs3rlZZSktLlZSUpOLi4mpFfAAAAL8Fv5gfbqXhjMlVzBUX+pwIzZG3YasqX3hTkmR166i4e27yOREAoLEz+8uOFc43bT/hGKtDqpzsvrKz+8hu1+bkbT2OiGnibT2MZ0LF92pF+qOF9hMX8U9WkK/2pYB7/LEjM/Gr7Z/6S4FoEPO98XIG9/c7BurR0fptSUmJEhMTI3rvWs1E37hxY0QftLb+9a9/ady4cfr2t7+tGTNmqEOHDrr77rv1wx/+MJyrsLBQY8eODV8TFxenkSNHavbs2brrrru0cOFCVVVVVRuTkZGhzMxMzZ49+6RF9IqKClVUVIT3S0tLJUme58ljZV8AABBF3AXHFna3B/fjvQp8YbpkSKkpUtE+mY3bFNxRJDv9HL9jAQAaGVN6QN6SNfIK1shs2nbC6ctW+3ahonl2b1ntUsLHPc+TldlTgVuuUvCdz6SSA8cuSk5Q4OqLZWX2bPrvlY62Pok78ekTdS+vz47mxphQb/njiu8nnl3/9WJ99S8DTjiz33Vlistk1m4+bQ639KCspv7/fTNXn/9s16qI3qVLl3oLcCobNmzQb3/7W02cOFH/8R//oXnz5unee+9VXFycvv/976uwMLRgRFpaWrXr0tLStHlz6B+ewsJCxcbGqk2bNjXGHL3+RB577DFNnjy5xvHdu3ersvIUPwsCAABoQHbRPiXu2C1JCrY/R/sVlIqKfE6F5io2s4dafbZPknTw8zkqH32uz4kAAI2BdbBcMWs2K3bVZjlbd52woOuek6zKvl1V1aeLvLZJoYPmJO970pKlH16jwLYiWQfKZVq3VLBjqmTbvE+KNgGFCv46i3bLnqfE3+2WVXbolF8GeP/6XAeXrVH5yMHy2rU5xUg0VmVlZfV27zNa7Wf79u366quvVFRUVKPCf++990YkmBT69mDo0KF69NFHJUmDBg3S8uXL9dvf/lbf//73w+O+vgKwMea0qwKfbsxDDz2kiRMnhvdLS0vVqVMntWvXjnYuAAAgagTnrtDRd2Nx52WrVWqqr3nQvJlRiaqauVgKBhW3YqNaXzdOVkyM37EAAFHIlB2Ut3StvILVMhu2hRbM/LrUlCMzzvsoNv0ctazrg6SnRyIqGgHvm2MU/OO/TjsuZsN2xWzcIXvYADljz5eVnNAA6dBQWrRoUW/3rnMR/bXXXtO//du/KTY2Vm3btq1WiLYsK6JF9Pbt26t//+q9ivr166e33npLkpR+5F+GhYWFat++fXhMUVFReHZ6enq6KisrVVxcXG02elFRkUaMGHHSx46Li1NcXM3fvdi2LZuVfAEAQBQwridv0crQjmMrMLj/WS+SBZyV1q3k5vSRt2C5VF4hLVkre1im36kAAFHCHDgkd+laefmr5K3bcsLCudWujeycvnJy+spKP+e0kyQBSbKz+8q61VbV1E+lkuNmIx9p46OKKgWnzZL2l0nGyJu3TN6iVXIuGqLA6OGyWtZf8RUNpz5rtnUuov/yl7/UL3/5Sz300EP1Xkw+//zztXr16mrH1qxZE24v061bN6Wnp2v69OkaNGiQJKmyslIzZszQE088IUkaMmSIYmJiNH36dF1//fWSpJ07d2rZsmV68skn6zU/AABAffLWbpbKDkqS7P49ZMXXeX4WEHGB3BxVLlguSQrm5cuhiA4AzZo5WB4qnBesCr138U5QOD8nWXZOPzk5fWS1b0fhHGfEGdhbdmZPeRu2SaUHpMTWsrt3DE8ycXL6yv1ykYKfzpEOV0jBoNzP5sqdU6DApblyzh8kK3BGTTvQDNT5b8ahQ4d04403Nshs7Pvvv18jRozQo48+quuvv17z5s3TK6+8oldeeUVSaOb7hAkT9Oijj6pXr17q1auXHn30UbVq1Uo33XSTJCkpKUl33HGHJk2apLZt2yolJUUPPPCAsrKyNGbMmHp/DgAAAPXl+AVFnSEDfEwCHGN1zZCVfo5M4R6ZTTvk7dgtO6Od37EAAA3IHDosd9mRGedrNksnWOzPapt8ZMZ5H1kZqRTOERGWbcvp2fnE52JjFBg9XM55AxX8JE/urMWhhUsPHVbwn5/L/XKRApdfIHtQf1k2fx9RnWXMiZpOndxPf/pTpaSk6Gc/+1l9Zarmvffe00MPPaS1a9eqW7dumjhxon74wx+GzxtjNHnyZP3ud79TcXGxhg8frhdffFGZmcdmvBw+fFg/+clP9Ne//lXl5eUaPXq0XnrpJXXq1KnWOUpLS5WUlKTi4mJ6ogMAAN+ZwxWq+NWLUlVQatVCcQ/fzcwZRI3gl4sUnPqJJMk5f5BivnWpz4kAAPXNlB+Wt2yd3PxV8tZsktwTFM5TkmRn9wm1aumYRuEcvvL2lSj44ZfyFq2QjquOWh1SFfjGKDm9u/qWDWfmaP22pKREiYmJEb13nYvorutq/PjxKi8vV1ZWlmK+tlDQ008/HdGA0YIiOgAAiCbBeUsVnPKhJIqUiD6m/LAqHn4p9CVPi1jF/epuWXGxfscCAESYOVxxrHC+euMJC+dqkyjnaOG8UzqFc0Qdb/suBd+bIW/1pmrH7T5dFRg/UnaHNH+Coc7qs4he5+lKjz76qD766CP16dNHkmosLAoAAID6580/rpXLUFq5ILpYLVvIGdRP7ryl0uFKufmrFBg+0O9YAIAIMIcr5C1fL7dglbxVG6WgW3NQcsKxwnnn9tSLENXsDmmKvet6uas3KfjeFzLbiyRJ3upNqlyzSfbg/oq5/EJZKUk+J4Wf6lxEf/rpp/WHP/xBt956az3EAQAAwOmYfSXy1m+VJFnt2sjq3N7nREBNTm52qIguyc0roIgOAI2YqaiUt2J9aMb5yg0nLpwntj5WOO+SQU9pNDpOn66ye90ib/EKBT+cJbOvRDKSt3CFKvJXy7lgkAJjcmXFt/Q7KnxQ5yJ6XFyczj///PrIAgAAgFpwF64IbztDM5ndhahkdW4vKyNVZkeRzJad8rbv4ufQANCImIpKeSs3HCucVwVrDkqIP1Y479qBwjkaPcu25AwZIDu7j9yvFis4PU86dFhyXbkzFsidt1SB0efJuWCwrNiY098QTUadi+j33Xefnn/+eT333HP1kQcAAACnYIyRu+C4Vi5D+vuYBjg5y7Lk5GYr+NZ0SaHZ6PZ1Y31OBQA4FVNZFSqcF6ySt2KDVFlVc1BCvJyBvUOF824dZNl2wwcF6pkVCCgwcpicc7MU/HSu3JkLpWBQKq9Q8L0ZCs5apMBlF8gZOoB/BpqJOhfR582bp88++0zvvfeeBgwYUGNh0bfffjti4QAAAFCd2bJTZnexJMnu0YnejIhqzpD+Cr77hVRZJXfhCgW+MYoFRgEgypjKKnmrNoYK58vXn7hw3rqVnIG9Zef0ld29I0VDNBtWyxaKGT9SgQsGKzhtltz5yyRjpP1lCk75UO6MBQpceZHsft35dWgTV+cienJysr75zW/WRxYAAACchrtgeXjbHpbpYxLg9KwWcaEFRucukSoq5S5aqUButt+xAKDZM1VBeas3hlq1LF8nVZygcB7fUk7WkcJ5j06yHArnaL6s5ATF3Hi5nJFDFXx/RuiXGpLMzt2q+v1bsnt0UuAbo2SzVlGTVeci+muvvVYfOQAAAHAaJujKXbwytBMTkDOwt7+BgFpwRmSHiuiS3Lx8iugA4BMTDMpbvUluwWp5y9ZKhytrDmrV4ljhvGcnWY7T8EGBKGa3b6fYH1wnb90WVb37hczWQkmSt36rKp99Q3ZOXwWuuFD2OW38DYqIq3MRHQAAAP7wVm4ILWwkyc7qJatFnM+JgNOzO7WX1TFNZtsumW275G0tlN0p3e9YANAsmKArb82RwvnStdLhipqDWsYdK5z36kzhHKgFu2dnxU64WV7BagU/mCmzZ78kyctfpcqla+Tk5igwdoSs1q38DYqIqXMRvVu3bqfs8bNhw4azCgQAAIATq76g6AAfkwB14+RmK/j3jyWFZqPbnS7zOREANF3GdeWt2SyvYLXcpWuk8hMUzlvEyc7qJSe7j+zeXWUFKJwDdWVZlpycvrIze8nNK1Bw+mzpwCHJ9eTOWiR3/jIFLjlXzkVDWROmCahzEX3ChAnV9quqqrR48WJNmzZNP/nJTyKVCwAAAMcxB8vlrVgf2kmIl927q695gLpwBvVT8F+fSxVVob7oV13MLykAIIKM68lbt1le/iq5S9eGf7lWTVzsscJ5n66yAjQnACLBCjgKXDhYzrABCn4+T+6MBaEFeisqFfxwloJfLVZg3Plyzh3I2gKNWJ3/jXnfffed8PiLL76oBQsWnHUgAAAA1OTmr5JcT5LkDOnPG3A0KlaLODmD+8vNK5Aqq+QuWqHAiEF+xwKARs24nrz1W48UztdIB8trDoqLkT2gl5ycPrL7dJMVQ+EcqC9WizjFXH6hAucPUvCjr0JrwnhGKj2o4N8/ljtjgQJXjpSd2fOUXT4QnSxjjInEjTZs2KCcnByVlpZG4nZRp7S0VElJSSouLlZycrLfcQAAQDNT8ewbMlt2SpJiH7hVdkaqz4mAuvG27VLl03+UJFkdUhU78RY+QAJAHRnvSOG8YLXcJWtCrSO+LjZG9oCeoTYTfbrKio1p+KAA5O3aq+AHM0PrERzH6tZBMeNHye7WwadkTdfR+m1JSYkSExMjeu+IfQX5j3/8QykpKZG6HQAAAI7wivaGC+hWRioFdDRKdsc0WZ3SZbYWymwvktlSKKtLe79jAUDUM54ns2Gb3KOF87KDNQfFxsju311Odl/Z/bpTOAeigJ3WVrG3XStv4zZVvTtDZtN2SZLZuF2Vz/9FdlYvBa68SHZqW5+TojbqXEQfNGhQtRkjxhgVFhZq9+7deumllyIaDgAAAJK7YEV42xnKgqJovJzcHAW3TpN0ZIFRiugAcELGMzKbtsvNXyW3YPWJC+cxAdn9e4R6nPfrzsKFQJSyu3VU7D03yVu2TsH3Z8gU7ZMkeUvXqnL5OjnDByow7nxZia19TopTqXMR/Zprrqm2b9u22rVrp1GjRqlv376RygUAAACFPkS7C5eHdixLzuB+/gYCzoIzqK+C//pMOlwpN3+VAldfIqslC4wCgHSkcL55x7HCeemBmoMCAdn9uod6nPfvQeEcaCQsy5KT1Ut2/x5y5y1RcNpXoS/HPCM3r0DuwhVyRg5V4OJzWXw9SkWsJ3pTR090AADgB3fdFlW9NEWSZPftrtg7r/M5EXB2qt6aLverxZKkwDfHKHDBYJ8TAYB/jDEym3fKLThSON9fVnOQ48ju1y3UqmVADwpsQBNgKirlzlyg4GfzpIrKYydat1Jg7Ag5udmyHMe/gI1Uo+iJDgAAgMjzFiwPbzvDaOWCxs/JzQ4X0d28AjnnD2KBUQDNijFGZkvhscJ5cWnNQY4tu+/RwnlPfrUDNDFWXKwCl44Itbr7eLbc2fmS50kHDin49idyZy5Q4IqLZGf34X1SlKh1Ed227dP+n2ZZloLB4FmHAgAAgGQqq0IfriWpRazsAT39DQREgJ2RKqtLhszmHTI7d8ts3iGrawe/YwFAvTLGyGzbJTd/lbyC1TL7SmoOcmzZfbqGCueZPWW1bNHwQQE0KKt1K8V8c4yci4Yo+MGX8vJXSZLMnv2q+tO/ZHVur5jxI2X37OxzUtS6iD516tSTnps9e7aef/550RkGAAAgcrxla8M/73Sy+8iKjfE5ERAZTm62gpt3SJKCeQWKpYgOoAkyxshsLzpWON+7v+Yg25bdu4vsnL5yMnvJakXhHGiO7HPaKPb7V8kbNUzBd7+Qt36rJMls2anKl6bI7t9dgStHym7fzt+gzViti+hXX311jWOrVq3SQw89pHfffVff/e539V//9V8RDQcAANCcuce3chlCKxc0HU5OXwXf+Uw6XCFv8SqZqy+hcASgSTDGyOzYLbdglbz8VTJ79tccZFuye3WRnd1XTlYvWfEtGzwngOhkd26vmLtvlLdyg4LvzZAp3CNJ8lZsUOXKjXKGZSpw2QWykhN8Ttr8nFFP9B07duhXv/qV/vjHP2rcuHHKz89XZmZmpLMBAAA0W6b0gLzVm0I7bRJlde/kax4gkqzYGDlDB8idtUgKBuUuWK7ARUP8jgUAZ8QYI1O459iM86J9NQdZluxenY8Vzlu3avigABoFy7Lk9O8hu283uQuWKzhtVmjRYWPkzlsqd9FKORcNUWD0cNo+NaA6FdFLSkr06KOP6vnnn1dOTo4+/fRTXXjhhfWVDQAAoNlyF62QjrTKc4YOkGWzoBCaFmdETqiILsnNy5dz4WAWzgLQqHjHF8537a05wLJk9+gkO6ePnKzeshLiGz4kgEbLsm0Fzs2Sk9NX7qxFCn4yRzpcEZqA8NlcuXMKFLg0N7RIe+CM5kmjDmr9Cj/55JN64oknlJ6erjfffPOE7V0AAAAQGe58WrmgabPTz5HVrYPMxu0yu/bKbNwuq3tHv2MBwCl5u/bKy18lt2B1uM1CNZZkde8kJ+fIjPPE1g0fEkCTYsXGKHDJcDnDByr4SZ7cWYsl15UOHVbwn5/L/XKRApdfIHtQfybe1CPL1HI1UNu21bJlS40ZM0aO45x03Ntvvx2xcNGktLRUSUlJKi4uVnJyst9xAABAE+ZtL1Ll/3tdkmR1yVDcfd/zNxBQT9wFy1X11/clSfaQ/or97nifEwFATV7RPnkFq+Tmr5bZubvmAEuyunWUk91XTnZvCucA6pW3r0TBD7+Ut2iFdFxV1+qQqsA3Rsnp3dW3bH47Wr8tKSlRYmJiRO9d65no3//+9/l5JQAAQANwFywLbztDmYWOpsse2Fua+qlUfjjUDuGa0SywByAqeLuL5RWslluwSmZ70QnHWN06hArnA3uzyB+ABmOnJCn2u+PljRqm4Hszwusome1Fqnr5/+T26arA+JGyO6T5G7SJqXUR/fXXX6/HGAAAAJAk43pyF60M7Ti2nJy+/gYC6pEVGyNn2AC5MxdKQVfugmUKjBzmdywAzZS3d7+8/COF8227TjjG6pIhJ6ePnIF9ZLWJ7CxHAKgLu0OaYu+6Xu7qTQq+90X4Cz9v9SZVrtkke3B/xVx+oayUJJ+TNg10nQcAAIgi3ppNUtlBSZLdvyezctHkObk5oSK6JDevQM5FQ/kFLICIMZ4nb8M2qfSAlNhadveOsmw7fN7bV3Ksx/nWwhPew+rc/ljhnGIUgCjj9Okqu9ct8havUPDDWTL7SiQjeQtXqCJ/tZwLBikwJpfPFWeJIjoAAEAUcRcct6DoMFq5oOmz09rK6t5RZsM2maJ9Muu3yurZ2e9YAJoAd8kaVU39VCopO3YwKUGBS3OlyspQj/MtO094rdUpXU52H9nZfWS3TW6YwABwhizbkjNkgOzsPnK/Wqzg9Dzp0GHJdeXOWCB33lIFRp8n54LBsmJj/I7bKFFEBwAAiBLmcIW8pWtDO/EtZfft7m8goIEEcnNUtWGbJCmYV6BYiugAzpK7ZI2qXn+n5omSMgX/8fEJr7E6psnJ7is7h8I5gMbJCgQUGDlMzrlZCn4690jLvKBUXqHgezMUnLVIgcsukDN0QLVf5eD0KKIDAABECbdgdehNriRnUD9ZAcfnREDDsLN7S++0lA6Wy1uyRubAIVmtW/kdC0AjZTwvNAO9FqyMVDk5R2act0up52QA0DCsli0UM36kAhcMVnDaLLnzl0nGSPvLFJzyodwZCxS48iLZ/brTRq+W+MoBAAAgSlRr5TKEVi5oPqxAQM6wzNCOG1pgFADOlLd+a/UWLicR+M4VinvgVgXG5FJAB9AkWckJirnxcsU+cKvs/j3Cx83O3ar6/VuqemmKvJO0tUJ1FNEBAACigLevRGb9VkmSlZoiq3O6z4mAhuWclx3edvMKZIzxMQ2Axsh4Rm7+KlW9+UGtxlsOJREAzYPdvp1if/Atxd59o6zO7cPHvfVbVfnsG6r80z/l7Sn2MWH0o50LAABAFPAWHjcLfegAflaJZsdOTZHds7O8dVtkdhfLW7dFTq8ufscC0AgY15O3eKWCn86R2bW39hcmtq6/UAAQheyenRV73/fkFaxW8IOZMnv2S5K8/NWqXLJWzogcBS7NlZUQ72/QKEQRHQAAwGfGGFq5AJKc3Gx567ZICs1Gp4gO4FSM68pdsFzup3PChaAwx5Zc7+QXJyfI7t6xXvMBQDSyLEtOTl/ZWb3k5hUo+PFs6cAhyfPkzlokd/4yBS45V85FQ2XFxfodN2pQRAcAAPCZ2bxTZnfo55N2z86y2iT6nAjwh53VW2rdSjpwSN7SNTJlB5kJBaAGEwzKnbdUwU/nSsWl1c5Z3ToqMDZX5nClgn/850nvEXPNaFk27VwANF+W4yhwwWA5Qwco+MV8uV/MlyqrpIpKBT+cpeBXixUYd76ccwfS/koU0QEAAHx3/CKK9lBmoaP5sgKOnGGZcj+fJ7nekZlQw/2OBSBKmMoquXMKFPxsnlR6oNo5u1cXBS7Nld2zc/iYdes1qpr6afVFRpMTFHPNaDkDezdUbACIalaLOMVcdoECI3IU/OgruXOXSJ6RSg8q+PeP5c5YoMCVI2Vn9mzWLScpogMAAPjIBINyF68K7cQE+FCPZs85LztURNeRli6jzpVlN98PbAAkU1Ep96vFCn4xP9Ry4Dh23+4KjM2V3bVDjeucgb1lZ/aUt2FbqOie2Fp2947MQAeAE7ASWyvm2+PkjByq4Psz5S1dK0kyRftU9dpUWd06KGb8KNndav77tjmgiA4AAOAjb8UGqfywpFArC6tFnM+JAH/Z7drI7t1F3prNMnv3y1u3WU7vrn7HAuADU14hd9YiBWcukA6WVztnZ/ZS4NLzZHdqf8p7WLYt57jZ6QCAU7NT2yr2tmvlbdyuqve+kNm4XZJkNm5X5fN/kZ3VS4ErL5Kd2tbnpA2LIjoAAICPqi0oOoxWLoAkObk58tZslnRkNjpFdKBZMQfLFfxyodwvF0rlFcdOWJKd3UeBMbmyM1L9CwgAzYDdrYNif3yTvOXrFHxvhkzRPkmSt3StKpevkzN8oALjzpeV2NrnpA2DIjoAAIBPzIFD8lauD+0kxsvu1cXfQECUsDN7SgnxUtlBeUvXypQeaDYf0IDmzJQdVHDGArlfLZIqqo6dsCzZg/uFiudpzWvmIwD4ybIsOZm9ZPfrIXfeEgWnfSWVHZQ8IzevQO7CFXJGDlXg4nOb/C9qKaIDAAD4xM1fJbmeJMkZ3J8ercARluPIOTdL7qdzJM+TO2+ZAmPO8zsWgHpiSg8o+Pk8ubPzpargsRO2LWfoADmjz5Pdro1v+QCgubMcW4HcHDmD+8uduSC0wHNFpVRZJXd6nty8AgXGjpCTmy3LcfyOWy8oogMAAPikWiuXoZk+JgGij3PeQLmfzZGM5M4pkHPJcBYYBZoYU1yq4Gdz5c5dIgXdYyccR87wLDmXDJedkuRfQABANVZcrAKXjpCTm6Pgx7NDX356nnTgkIJvfyJ35gIFrrhIdnYfWVbTet9GER0AAMAH3q69Mlt2SpKsDqmyM9r5nAiILnbbZNm9u8lbvVFmX4m8NZvk9O3mdywAEeDt3S/30zly5y8L/yJLkhQIyMnNDrUFSE7wLyAA4JSs1q0U880xci4aouAHX8rLXyVJMnv2q+pP/5LVub1ixo+U3YQWdqaIDgAA4AN34fGz0FlQFDgRJzdb3uqNkiQ3L58iOtDIeUV7Ffx0rryFyyXPHDsRGyPn/EEKjBomKyHev4AAgDqxz2mj2O9fJW/UMAXf/ULe+q2SJLNlpypfmiK7f3cFrhwpu33jnzBEER0AAKCBGc8ca+ViW3IG9fM3EBCl7AE9pMR4qfSgvOXrZErKZCUxOxVobLyduxX8JE9e/mrJHFc8bxEr54IhClw0RFbrVv4FBACcFbtze8XcfaO8lRsUfG+GTOEeSZK3YoMqV26UMyxTgcsuaNS/MqKIDgAA0MC89Vuk/WWSJLtPN1mJrX1OBEQny3HkDB8od3qe5Bm585YqcOkIv2MBqCVv2y4Fp+fJW7qm+omWLRS4aIici4bIatnCn3AAgIiyLEtO/x6y+3aTu2C5gtNmhT7zmNB7OHfRSjkXDVFg9PBG+e9+2+8AdfHYY4/JsixNmDAhfMwYo4cfflgZGRlq2bKlRo0apeXLl1e7rqKiQvfcc4/OOeccxcfH66qrrtK2bdsaOD0AAECIt4BWLkBtBYYPlI6sSxWcs0TG8059AQDfeZt3qPL3b6ny6T9WL6C3bqXAlSMV94u7FBh3fqMsogAATs2ybQXOzVLcz36gwPiRUou40IlgUO5nc1Xx368oOGO+TDDob9A6ajRF9Pnz5+uVV17RwIEDqx1/8skn9fTTT+uFF17Q/PnzlZ6erksvvVRlZWXhMRMmTNDUqVM1ZcoUzZo1SwcOHND48ePluu7XHwYAAKBemcoquUtWh3ZaxMoe0NPfQECUs1KSZPftHtopLpW3aqO/gQCclLdhqypf/j9V/s+f5a1Yf+xEYrwCV1+suJ/fGZqBeLSgAgBosqzYGAUuGa64n98pZ9QwyXFCJw4dVvCfn6vy8VflLlwuc/waGVHMMsZEfdIDBw5o8ODBeumll/TrX/9aOTk5evbZZ2WMUUZGhiZMmKAHH3xQUmjWeVpamp544gndddddKikpUbt27fTGG2/ohhtukCTt2LFDnTp10gcffKBx48ad8DErKipUUVER3i8tLVWnTp20d+9eJScn1/tzBgAATZO7aKXcv74vSbKHZynw7RO/FwFwjLd8nYKvvSNJsgb0UMxt1/obCECYMUZm3Ra50/NkNnztF9/JCXIuPlf2uVmyYugmCwDNmdlXIvejr+QtWiEdV422OqTKuXKk7N5dzvoxSktL1aZNG5WUlCgxMfGs73e8RvFfsR/96Ee68sorNWbMGP36178OH9+4caMKCws1duzY8LG4uDiNHDlSs2fP1l133aWFCxeqqqqq2piMjAxlZmZq9uzZJy2iP/bYY5o8eXKN47t371ZlZWUEnx0AAGhO4vMWK+bIdkn3DLlFRb7mARqFtq2V2LqV7AOH5K3YoN3rN8okxPudCmjejFFg4w61mL1EgR27q51yk1urYniWKjO7h2YeFu/zKSQAIKqMHions7tazFiomE07JUlme5GCr/xdVV3b6/DIIXLTUs749sd3Jom0qC+iT5kyRYsWLdL8+fNrnCssLJQkpaWlVTuelpamzZs3h8fExsaqTZs2NcYcvf5EHnroIU2cODG8f3Qmert27ZiJDgAAzogpOaCqI28WlZKklEGZsmzL31BAIxHMzZY3PU+WMUpZv1POWBYYBfxgPCOzYr3cT/Jktu2qfrJdGzmjz1PMoH5q6TSa7rEAgIaUmipl9ZW3ZrPc92fIbA9NKorZtFMxm9+TPai/nMvOl5WSVOdbt2hRf2ttRHURfevWrbrvvvv08ccfn/JFsKzqHz6NMTWOfd3pxsTFxSkurmafNtu2Zdu8GQAAAHUXzF8lHemk5wzpLyfg+JwIaDxicnNU8ckcyRi585YqcOkIWRTpgAZjPE/ekjUKTs+T2Vl95rmVfo4Cl+bKzu4ji8/LAIBasPt2k9O7q7zFKxX88EuZfSWSkbxFK+QVrJZzwSAFxuTKim9Z+3vW43+DorqIvnDhQhUVFWnIkCHhY67raubMmXrhhRe0enVoUa7CwkK1b98+PKaoqCg8Oz09PV2VlZUqLi6uNhu9qKhII0YwewUAADQMY4zc+cvC+87QAT6mARofKzlBdv/u8pavl/aXyVu1QQ4L8wL1zrheqMDxSZ5MUfW2LFaHVAUuHSE7sxe/rAIA1JllW3KG9Jed3VvuV/kKTp8tHTosua7cGQvkzl2qwOjz5Fw4WFZszOlvWI+i+ivi0aNHa+nSpcrPzw//GTp0qL773e8qPz9f3bt3V3p6uqZPnx6+prKyUjNmzAgXyIcMGaKYmJhqY3bu3Klly5ZRRAcAAA3G7CiSKdwjSbK6Zshud+a9/oDmysnNCW+7efm+5QCaA+O6Cs5dosrHf6+qv75frYBudW6vmB98S7ETb5EzsDcFdADAWbECAQVGDlXcz++Uc8lwKXBk3vfhCgXfn6GKx3+v4LylMp7nW8aonomekJCgzMzMasfi4+PVtm3b8PEJEybo0UcfVa9evdSrVy89+uijatWqlW666SZJUlJSku644w5NmjRJbdu2VUpKih544AFlZWVpzJgxDf6cAABA8+QuWB7eZhY6cGbsvt2kNolScam8lRtliktltUn0OxbQpJhgUO68pQp+OlcqLq12zureMTTzvHeX07ZQBQCgrqyWLRQzfqQCFwxWcNqs0C95jZH2lyk45UO5X8xXYPxI2f26N/h/h6K6iF4bP/3pT1VeXq67775bxcXFGj58uD7++GMlJCSExzzzzDMKBAK6/vrrVV5ertGjR+v111+X49CHFAAA1D/jenIXrgjtOI6c7L7+BgIaKcu2FRg+UMFpsyRjFJxToJjLL/Q7FtAkmMoquXMKFPxsnlR6oNo5u1eXUM/znp19SgcAaE6s5ATF3Hi5nJFDFXx/prwV6yVJpnCPqn7/luwenRT4xijZnY+19zaeJ3f91vrLZMyR1a1wSqWlpUpKSlJxcbGSk5P9jgMAABoRd8V6Vf3+LUmSPbC3Ym+9xt9AQCNmSspU8V8vS56RElsr7hf/xgKjwFkwFZVyv1qs4BfzpQOHqp2z+3UPFc+7dvApHQAAkrdui6remyGzZWe143ZOHwWuuEhmx25VTf1UpUW7lfbSr1RSUqLExMj+WrHRz0QHAACIdrRyASLHSkqQ3b+nvGVrpdID8lasl5PVy+9YQKNjyivkzlqk4MwF0sHyaufszF6h4nmndJ/SAQBwjN2zs2Lv+568gtUKfjBTZs9+SZKXv1qVBWtCLV/qGUV0AACAemTKK+QtWxfaiW8pu293fwMBTYCTmx0qoiu0wChFdKD2zMFyBWcukPvlIulwxbETlmRn91FgTK7sjFT/AgIAcAKWZcnJ6Ss7q5fcvAIFP54d+gVVAzVZoYgOAABQj9yC1VIwKElyBvWTFWBNFuBs2X26yUpJktlXIm/1Rnn7SmSnJPkdC4hqpuyggjMWyP1qkVRRdeyEZcke3C9UPE9r619AAABqwXIcBS4YLGfoAFW9/Ym84371W58oogMAANQjd8Gy8LYzjFYuQCRYtiXnvIEKfvClZCR3ToHsKy7yOxYQlUzpAQU/nyd3dr5UFTx2wrblDBsg55LzZLdr41s+AADOhNUiTk7fbhTRAQAAGjtv736ZDdskSVZaW1kd6S0LRIpzbpaC076SPE/u3KUKjDtflsMvPYCjTHGpgp/NlTt3iRR0j51wHDnDsxS4ZLgsfsEBAGjMEls32ENRRAcAAKgn3sIV4W1nyABZluVjGqBpsRJby87sKW/JGqnsoLxl6+Rk9/E7FuA7b+9+uZ/MCf0SyvWOnQgE5ORmK3DxubKSE/wLCABAhNjdO0pJCVJJWb0/FkV0AACAemCMOdbKxZKcIf39DQQ0QU5uTqiILsnNK6CIjmbNK9qr4Cdz5C1aIXnHLbIWGyPn/EEKjBomKyHev4AAAESYZduKuXa0ql5/p94fiyI6cBLG8+Rt2CaVHpASW8vu3lGWbfsdCwDQSJjNO2T27Jck2T07y2qT6G8goAmye3WR1TZZZu9+eWs2ydtTLPscejujefF27lbwkzx5+auk42rnahEr58IhClw0VFZ8S9/yAQBQn5yBvaVbr1HV1E+losP19jgU0YETcJesCf3Dd/zPQZISFHPt6NA/nAAAnIZ73AI3ztBMH5MATVdogdFsBd+fIUly5yyRPX6kz6mAhuFt26Xg9Dx5S9dUP9GyhQIjh8q5cLCsli38CQcAQANyBvaWndlTMQUrpZd+VS+PQRG9jrwNW2VyEpmR3IS5S9ac+GcgJWWh47deQyEdAHBKJhiUu3hlaCc2RnZWL38DAU2Yc26mgtO+lFxP7rylClx2gawAC4yi6fI27wgVz1esr36idSsFRg6Tc36OrBZx/oQDAMAnlm3L6dGp3u5PEb2Ogq9OVUXqLGYkN1HG80Iz0E+h6p1PZWf25IsUAMBJecvXS+UVkiQ7qxfFDKAeWQnxsrN6yctfLR04JG/ZWjk5ff2OBUSct2Grgh/nyVuzqfqJxHgFLj5XznnZsuJifckGAEBTRxH9TDTTGcnGGMkYyfNCq7x7R7bD+57M0X3PhI8dP8Ycf81x1x29xnz9Gs+T3GPXmBNcU6fHN+Zrj1v9OZiKSulg+alfiP1l8jZsk9Ozc8O88ACARsddSCsXoCE5uTmhIrokNy+fIjqaDGOMvLVbFJw+W2b91uonkxMUGH2enHOzZMXw0R4AgPrEf2nPQtXUT2R16yBLajxF5NM9fo1j1R8fIcGPZ0sHy0MLxbFIDwDgOObAIXkrNoR2ElvL7sWXrkB9s3t2ltWujczuYnlrt8jbvU92uxS/YwFnzBgjb+UGBT/Jk9m0o9o5q22ynNHD5QzNpHURAAANhCL62Sg5oMpfveh3CpwJ25Js+9gf58j/eq508PQr+Zp1W1S1botkWbI6pcvu01VO766yumTwRhYAmjl38arwF8/OkP60/wIagGUdWWD03S8kSW5egeyrLvY1E3AmjGfkLV8Xmnm+bVe1c1ZqigKjz5M9uL8sh/+2AADQkCiiN1cnKyIfOW5VO3bs+NFjVvi6r93nyBjrpPcP3cty7BNcd+yas3/8mtdUe3zLOuHLYjxPFf/1O6mkrHavozEyW3bK3bJT7vQ8KS5Gds/Osnt3ld2nq6x2KSd9LABA0+QuWBbedoYO8DEJ0Lw4wzIV/OBLyXXlzl+mwBUXygrwcQeNg/E8eUvWKDg9T2bn7mrnrPRzFLg0V3Z2H76YBQDAJ7yrPEtWl/ayEuKbTBG5ubNsWzHXjg71vD+JwPfGy2rVUt7qjfJWb5Ip3HPsZEWVvOXrQwvKSVJygpw+XUNF9V5dZLVuVb9PAADgK2/XXpmthZIkq2Oa7PbtfE4ENB9W61ayB/aWt3ildLBc3pI1cgb39zsWcErG9eQtXhlq21K0r9o5q0OqApeOkJ3ZK/T5EgAA+IYi+tlITlDsPd9lNkAT4wzsLd16jaqmflp9RnpygmKuGR1eTNbp202SZErK5K3ZLHfNJnlrNktlB49ds79M7tylcuculSzJ6pAmu0832b27yO7WgdlRANDEuAuOW1B0CLPQgYYWyM1W5eKVkqRgXgFFdEQtE3TlLlwu95M5Mnv3VztndW6vwNgRsvt1Z/ITAABRggreWYi5ZjQF9CbKGdhbdmZPeRu2SaUHQgvDde94wv+/raQEOcMy5QzLlDFGZudueas3hf5s2CYFg6GBRjLbdsndtkvup3Ok2BjZPToda/2S1pY3yQDQiBnPyF14pIhuW3IG9/M3ENAMWT06yUpNkSnaJ7N+q7xde2WntfU7FhBmgkG5c5cq+Nlcqbi02jmre8fQzPPeXfhcAABAlKGIfia+NiMZTZNl23J6dq7bNZYlKyNVdkaqdPG5MpVV8jZul7cmVFQ3O4qODa6skrdyg7yVG0L7ia2PtX7p3SXUJggA0Gh467dI+0O/YLL7duPf44APLMuSk5ut4D8/lyS5cwpkX32Jz6kAyVRWyZ1ToOBn80KTdI5j9+4SKp736ORTOgAAcDoU0esocMf/Z+/e43Ou/z+OPz/XtfNssxmbMSynMHLMoXLIqZCkovCNqBSJkNLJ9C2iHH7ROaGcOlHq+6UQ+krlUOQUlTllaw6zDbPDdX1+f4yLy1wsrmvXZo/77Xbd2vX+vD/v9/vTtWuz5957fe6Qf/067EBHgRh+vrLWrCJrzSrSbZKZcUL2XXtk27lH9l17pPRzSr+kH5dt/VbZ1ufdkM6oUO70LvW4vNIvvrxdAaAocyrl0jjeiysBSjZr43jl/uc7KffMDUZb8u8oeI2ZlS3b978od9V66fhJp2OWWtfk3TC0SgUvrQ4AABQU/5r8hyzXxBKg47IZIcGyNqoja6M6eaVfkg87dqnb/9wv5eQ6+pp/pcj2V4psK9dJvj6yXBMrS83KstSIk1E+kj/xBIAixMzKln3zzrwnAf6y1K7q3QUBJZgRHCjLdTVl37hdOnlK9s07ZW3MPQpQuMzMLNnWbFTu6g3SyVNOxyzx1fPC89hoL60OAAD8U4TogJcYhiGjfFlZypeVWjWRmZMr+56/8gL1XXtkHvj7bOecXNl3Jsq+M1HSKikkWJaaVWQ9U/oltJS3LgMAIMm+5XcpO0eSZK1fU4afr5dXBJRsPs3rK3vjdkmnbzBKiI5CYp7IVO53G2T738/SqayzBwzJcl1N+bRrnlf6EQAAFCuE6EARYfj6yFq9sqzVK0tqJfP4Sdl/3yv7zj2y7drjqLMrSco4IfuGbbKfLh1glC8rS828G5Ra4ioS3gBAIXPcUFSUcgGKAiOugoyoMjL/PiIz8YDsyYdliY709rJwFTMzTih39QbZvv9Zyso5e8BiyNKwtnzaNuMmtwAAFGOE6EARZZQKkrVBLVkb1JKPacpMOZq3G33XHtn/2O/Y8ShJZtIh2ZIOybZqveRjleWaiqfrqVeRUb6cDAulXwDAU8xjGbLv2itJMiLCZMRR2xbwNscNRj//VpJk+2GzLHe09fKqcDUy048rd+U62dZucirNKItF1iZ1ZG3bTJbIcK+tDwAAuAchOlAMGIYhI6pM3u6Vlo1l5tpk7vlLttP11M0DyZJ5unOuTfZde/MCna9WS6WCZKlRWdaacXmlX8JCvHotAHC1sf28XTLzvghbGtfhnhVAEWFtHK/cr76TcnNl27BVPp1b8td6cBszNV253/4k20+/Srm2swesVlmb1pXPzU1lRIR5b4EAAMCtCNGBYsjwscqoVkmWapWkTi1lnsh0Lv2Smn628/GTsv+8Q/afd+SdGx2ZV/alRhVZrqkow9/POxcBAFcB0zRl23BuKRfqLgNFhREUIEv9mnnl7zKz8m4w2oRyS7gy9iPHZFv+o2wbtko2+9kDvj6yNr9OPm2uZ9MKAABXIUJ04CpgBAfKWv9aWetfm1f65VCq4wal9j/2OtVlNJMPy5Z8WLbVGySrVZa4Co5Q3agQRekXAPgHzL9SZCYfliQZVSrwJ/tAEePTvL6yT/+iK/eHTYTouGz2lCPKXf6j7D9vl+zm2QN+vrLe0EA+rZvICAn23gIBAIBHEaIDVxnDMGSUi5ClXIR0U0OZNpvMvQdl23m69Mv+ZEfZAdlssv+xT/Y/9kn/+U4KDpSlemVZalaRtUYVGeGh3r0YACjibBu2Oj5mFzpQ9BhVYmRER8pMPixzz0HZDx6SJaast5eFYsSedEi5y3+QfdNvZ8snSlKAn6w3NZJPy8YyggO9tj4AAFA4CNGBq5xhtcq4JlaWa2KlW2+SefJUXumXM/XUj6ad7XwiU/ZNv8m+6TflSnlh/Ola6pZqlSj9AgDnMG022U6XypLVKmv9a727IAD55N1gtL5yFy2XJNl+2CTLne29vCoUB/YDfyt32VrZt/zufCAoQD4tG8t6U0MZgQHeWRwAACh0hOhACWMEBch6XU1Zr6sp0zRlHj7mCNTtf+yVTmU7+popR2VLOSrb/zZKVouMyjF5NyitWUVGxSgZFosXrwQAvMu+c490/KQkyRJfTUYQYQpQFFkb11buV6uknFzZNm6TT5dWbAyAS/a9B/PC8+27nQ+UCpJP6yaytqgvI8DfO4sDAABeQ4gOlGCGYcgoGy5L2XDphgYybXaZ+5Jk37VHtp17ZO47eLbmo80uc/cB5e4+IC35nxQYkLdDvUYVWWtWkRER5t2LAYBCRikXoHgwAgNkbVBLtnVbpFPZsm36TT5N63l7WShkpt0u++4DUvpxKbSULNdUdNoQYt+9X7nf/CD7rj3OJ4YGy6dNU1mbXyfDz7dwFw0AAIoMQnQADobVIiOugixxFeTT8QaZmVl5NdN3Jsq+a4/Mw8fOds48JfvmnbJv3plX+qVsuCw1quTdpLRaJXboALiqmZmnZN/6R96T4EBZro3z7oIAXJS1+XV5Ibok2w+bCdFLGNuvu5SzaIWUlnG2MSxEPt1ulhEYoNxla2X+ud/5pNIh8mnbTNbr68rw5cdmAABKOv41AMAlI9Bf1rrVZa1bXZJkP3JO6Zff90qZWY6+5qFU2Q6lyvb9L5LFkFG5gqw1KstSM05GbLQMK6VfAFw9bJt3Srk2SZK1YW0ZVquXVwTgYoxK5WXElJN5MCXvr+7++luWClHeXhYKge3XXcqZ9Xn+A2kZyp39Rb5mo0xpWds2k7VxHRk+fG0HAAB5CNEBFJilTGlZmteXmteXabfL3J8s+849su3aI3PPQcluz+toN2UmHlBu4gHp6++lAH9ZqlfO26Ves4osZUp78zIA4IrZNmxzfEwpF6Doy7vB6HXK/WyZJCl36RpZG9S6YFkPXB1M05SZk6uc0zeVvRSjXIR82jWXpUEtNn8AAIB8CNEBXBbDknejUUvlGPl0aCHzVJbsf+7P26W+a4/MlKNnO5/Kkn3LLtm37Mo7t0zpvDC9RhVZqleSEcjN+AAUH/Yjx2TuPiBJMqLKyKjIblagOLA2qq3cL76Vcm2yb/tT9m1/5h0IC5HvHW1lrVfDuwsswkzTlGx2yWY7+99cm0y7Pe+vcly2nznHJtOWv002u8zT55w7hnnuXLkX6Gu/QPuZvvZz/ltA1vbN5dPxBn6ZAgAAXCJEB+AWRoC/rHWqyVqnmiTJTE2Xbece2Xclyr5rr3TylKOveeSYbGs3ybZ2k2QYMiqXP3uD0krlKYsAoEizO+1Cj5dhGF5cDYCCsu/a6yjD5CQtI6/cR79uhRKkm6Z5TghsPy8Itkm5LkLmc4JjR8icaz8vfHYOsy/cfoG2cwPwC7XbCh5IF0eWqDIE6AAA4KII0QF4hBEeKp9m9aRm9fJKv/z1d94u9Z17ZN/z19kfxkxT5p6Dsu05KNs3ayV/P1mqV5KlZpwsNSrLiAwnoAJQZJimebaUi5G3sxVA0Wfa7Xk3lryInI+WyH70mAybKdltMs8LufMF2OeG1+f3zbfL+rxzcGWsFslqPftfH2teCO5jdWo3s7OlpMOXHi+0lOfXDAAAijVCdAAeZ1gsMmLLyxJbXmrXXGZWtnPpl7+PnO2clS371j9k3/pH3rkRYbKcvkGppVolGcGBXroKAJDMPQdlHjkmSbJUqyyjdIh3FwSgQOy7D0hpGRfvlJkl2+JVhbKeIuX8QNpqyfurQB+rdF4wbVhPf+xzXt9zwuwz5zj6Oo7l/ddwzHP6mKPvpdotjvELusHCtNuV9e+3L/7alw6R5ZqKbvqfCQAArlaE6AAKneHvJ2vtqrLWripJMo9lyLZrjyNU14lMR1/zaJpsP/4q24+/5pV+iY2WpWYVWWtUkVE5RoYPpV8AFB7bhq2Oj7mhKFCMpB8vnHkslvPC50uFzOe0W88PmV3tsj43ZD4veD5zvs/5AfaFd2rLWvBAujgyLBb53tE2r1yPC77d2lLKBQAAXBIhOgCvM0qHyOf6utL1dWXaTZkHU04H6omy7/4r70+fpbzSL/uSZNuXJNuyHyR/X1mqVnLcpNQoF3FV/yAIwLvMnFzZNv2W98TPVxZuQggUHwUs12Ht0EKWClH5A20f57D6gju1LRYZFv4dUtRY69WQ+nXLK+dz7o700iHy7cYNZQEAQMEQogMoUgyLIaNilCwVo6S2TWVm5+SVfjm9U91MPqeuZVaO7Nv/lH37n3nPS4fIejpQt1SvLKNUkHcuAsBVyb79TykzS5JkqVdDhr+fl1cEoKAs11SUwkIuWdbDp0MLdiVfhaz1asgSXy2vrE/6cSm0lCzXVOS1BgAABUaIDqBIM/x8Za11jay1rpEkmWkZsu/aK9uZ0i/HT57tfCxDtp+2yPbTFsmQjApRjhuUWuIqyPDhSx6Ay0cpF6D4oqwHDItF1mqVvL0MAABQTJEoAShWjLAQWZvEy9okPq/0S9Ihxy51++4DUm5uXkdTMg/8LduBv2Vb8WNe6YWqsXm71GtWkRFVxmXpF9NuZ6cSACfm8ZOy70jMexJWShaCGKDYoawHAAAALhchOoBiy7AYMiqUk6VCOanN9XmlXxL/kn1nouy79so8mHK2c3aO7Dt2y75jd97z0FJnS7/UqCwjJFiSZPt1V/4frsNC5HsHP1wDJZntlx2S3S5Jsjaqwy/WgGKKsh4AAAC4HIToAK4ahp+vrDWryFqziiTJzDgh+649Z0u/pJ842zn9uGzrt8q2Pq88g1GhnIzwUNm3/pF/4LSMvD//7teNIB0ooWwbtjk+ppQLULxR1gMAAAD/VJHecjF+/Hg1adJEISEhKleunLp166adO3c69TFNUwkJCYqJiVFgYKBat26tbdu2OfXJysrSkCFDFBkZqeDgYHXt2lUHDhwozEsB4AVGSLCsjerIr1dn+Y8ZJL8n7pdP1zayXBsn+Tr/DtH8K+XCAfo5cj5fIfP0TlQAJYc9+bDM/cmSlHfj4+hIL68IAAAAAFCYinSIvnr1ag0ePFg//vijli1bptzcXHXo0EEnTpzdTTpx4kRNnjxZ06dP1/r16xUdHa327dsrI+NsKYZhw4Zp0aJFWrBggdasWaPjx4+rS5custls3rgsAF5gGIYs5cvKp3UT+T10t/xffEy+j/SU9eamMipGFWyQYxnKfudT5X77k2w7dstMTZdpmp5dOACvc96FHu/FlQAAAAAAvMEwi1ECdOjQIZUrV06rV69Wy5YtZZqmYmJiNGzYMD355JOS8nadR0VFacKECRo4cKDS0tJUtmxZffjhh+rZs6ck6eDBg4qNjdV///tfdezYsUBzp6enKywsTKmpqSpdurSnLhGAl+Su3aTcT7/55ycG+MmILitLdKSM8pEyoiNlKV9WRqkg9y8SQKEz7XZlvfi2dCxDsljkP+YRxz0UAAAAAABFx5n8Ni0tTaGhoW4du1jVRE9LS5MkRURESJISExOVnJysDh06OPr4+/urVatWWrt2rQYOHKiNGzcqJyfHqU9MTIzi4+O1du1alyF6VlaWsrKyHM/T09MlSXa7XXbKOQBXn8jSl3feqWyZe/6Sbc9fzu3BgTLOCdaN6EgZUZEyAv2veKkACo/99715Aboko2YVmcGBlHUCAAAAgCLIk5ltsQnRTdPU8OHDdeONNyo+Pu9PqZOT8+qTRkU5l2KIiorS3r17HX38/PwUHh6er8+Z8y9k/PjxGjt2bL72Q4cOKTs7+4quBUARVMpPoSFBMjJOyrjAYVOSWSpIJ+5oLcvRNFkPHZP1cN7Dcu4NS884kSnzz/0y/9zv1GwPCZKtbLhskaVliywte2Rp2cqE5avRDqBoCFqzUX6nPz5evaJyUlK8uh4AAAAAwIWdW97b3YpNavPoo4/q119/1Zo1a/IdMwznyMs0zXxt57tUn9GjR2v48OGO5+np6YqNjVXZsmUp5wJcpezd2yl39uILHjMk+d7ZTgF1a+Q7Zp7Kkvn3EZnJh50eyjiZr68l46QsGSflu/ucneuGpDKlz+5YP/MoGy7DanXT1QH4p8ysbOX8fvoXYQH+Kt28oQx+4QUAAAAARVJAQIDHxi4WPwkOGTJEixcv1nfffaeKFSs62qOjoyXl7TYvX768oz0lJcWxOz06OlrZ2dlKTU112o2ekpKiFi1auJzT399f/v75yy5YLBZZLEX6fqwALpPlumtl9LMoZ9EKKe2c316WDpFvt7ay1ssfoEuSggKluIp5j3OYx0/KnnxYZtJhmcmHHB/rVJbz+aakw8dkHj4mc+sfZ9utFhllI2SUj5QluuzZcL1MmAy+DgEeZ9v2p5SdI0my1r9WVn+/S5wBAAAAAPAWT2a2RTpEN01TQ4YM0aJFi7Rq1SrFxcU5HY+Li1N0dLSWLVumBg0aSJKys7O1evVqTZgwQZLUqFEj+fr6atmyZerRo4ckKSkpSVu3btXEiRML94IAFHnWejVkia8m++4DUvpxKbSULNdUvKzQ2igVJGu1SlK1So420zSltON5gXryIZlJh09/fFjKyXUewGZ37Gq367ez7b4+MqLKyCh/+oamp29mqrBSl/wrHAAFZ9uw1fGxtUkdL64EAAAAAOBNRTpEHzx4sObNm6cvvvhCISEhjhrmYWFhCgwMlGEYGjZsmMaNG6fq1aurevXqGjdunIKCgtSrVy9H3wEDBmjEiBEqU6aMIiIiNHLkSNWtW1ft2rXz5uUBKKIMiyUv/PbE2IYhlQ6RtXSIdO3ZXwyadlPm0WNnQ/Ok02VhUo5ItvNujJGTK/PA3zIP/C2nIwH+eYH6OTc0tZQvK6NUkEeuBbiamccy8m4qKskoU1pGlQpeXhEAAAAAwFuKdIj+5ptvSpJat27t1D5z5kz169dPkjRq1ChlZmZq0KBBSk1NVdOmTfXNN98oJCTE0X/KlCny8fFRjx49lJmZqbZt22rWrFmyUmsYQBFhWAwZkeFSZLgUX93RbtpsMg+lng7WD52tuX44Na8MzLlOZcnc85dse/5ybi8VJEv5SBmnS8JYTgfsRkD+klUA8th+3u54j1kb1+GvPAAAAACgBDNM0zw/hsEFpKenKywsTKmpqdxYFIDXmdk5MlOOOJWDsScfllLTCz5I6ZC8Wuvlz9m9Xq6MDD9fzy0cKAZM01T2xPdl/n1EkuT3zEOylCnt3UUBAAAAAC7qTH6blpam0NBQt45dpHeiAwAuzPDzlVExWqoYrXP/psbMzJL59znlYJIPyZ50WDp+Mv8gxzJkP5Yh/bZbNsfAhozI0o6bmFrKn76hadlwGfz1DkoI86+/HQG6EVeRAB0AAAAASjhCdAC4ihiB/jKqVJDlvPrN5vGTeTvWk04H66c/1qks5wFMM698zKFUacvvZ8N1q0VG2YjTu9bLOmquGxGlZVgoc4Gri239NsfH1sbcUBQAAAAASjpCdAAoAYxSQXk3Sz3nhqmmaUppxx211vOC9UN5O3Bzcp0HsNnP3vRUv51t9/OVEVXmbDmYqLzd6worRQ1pFEumzSbbLzvynvhYZa1f07sLAgAAAAB4HSE6AJRQhmFIpUNkLR0i1brG0W7a7TKPpuWF5kmHZU8+lLeDPeWoZLc7D5KdI3N/smz7k53bA/zP1lo/t+56qaBCuDLg8tl/S3SUP7LUqSYjMMDLKwIAAAAAeBshOgDAiWGxyIgMlyLDpfjqjnYz1ybzcKrMpEOOm5mayYdlHk6Vzr9F9aksmYl/yZb4l3N7SPDpYP1MzfXTHwf4e/7CgAKwbaCUCwAAAADAGSE6AKBADB+rjOhIKTrS+Wam2TkyU46c3rV+dve6jmXkHyTjhOwZJ6Tf9zq3h4c6wnXHzUzLRcjw8/XoNQHnMk+ekn3bH3lPSgXJcm2cdxcEAAAAACgSCNEBAFfE8POVUTFaqhjtHK5nZp2utX5IZvKRvBuaJh12lMpwkpoue2q6tGP32ZuZGoaMyNLn7Fo/Ha6XDZdhteYfA7hCts2/Sbl5n4HWBrX4PAMAAAAASCJEBwB4iBHoLyOugixxFZzazYwTZ8vBnNm9nnxIOpXtPIBpyjyUKvNQqrTl97PhutUio1wZ53Iw0ZEyIkrLsHAzU1w+p1IuTSjlAgAAAADIQ4gOAChURkiwrCHBUvXKjjbTNKVjGY5wPa8szCGZfx+RcnKdB7DZ844lHZL9l3Pa/XxlRJXJKwtTPu+GppboSCmsVN5NVC/BtNtl331ASj8uhZaS5ZqKMiwWN101ijLTbpftlx0yz9Twjyojo0KUdxcFAAAAACgyCNEBAF5nGIYUHipreKhU6xpHu2m3yzySdvompnnlYMzkwzJTjkp2u/Mg2Tky9yfLtj/ZuT3QPy9QP71r3XFj01JBji62X3cpZ9EKKe2cOu5hIfK9o62s9Wp44pJRRFzwtU/LkH3L77z2AAAAAABJkmGapuntRRQH6enpCgsLU2pqqkqXLu3t5QBAiWbm2mQeTs3bjX66LIyZfEjmkWNSQb+rhQTn7VT3scq+Y7fLbr79uhGmXqVsv+5SzqzPXR7ntQcAAACA4uNMfpuWlqbQ0FC3js1OdABAsWP4WGVER0rRkc43M83Okfn3kbySMEmHHKVhdCwj/yAZJ2TPOHHJuXJmf6GcsFJSAUrCoBgxTSnt+EW75Hy+Qpb4apT1AQAAAIASjhAdAHDVMPx8ZcRGS7HRzuF65imZyUdkTz50etd6XsiuE5mXHvR0vXaUQMcyZN99QNZqlby9EgAAAACAFxGiAwCuekZggIy4CrLEVXBqz137i3I/XXbpAQL9JV++ZV5VcnKlzKxL90u/+G51AAAAAMDVj0QAAFBiGeXKFKif7/13sBv5KmP7Y59y3lhw6Y6hpTy/GAAAAABAkUaRTwBAiWW5pqIUFnLxTqVD8vrhqsJrDwAAAAAoKEJ0AECJZVgs8r2j7UX7+HZry40lr0K89gAAAACAguInQwBAiWatV0O+/brl35VcOkS+/brJWq+GV9YFz+O1BwAAAAAUBDXRAQAlnrVeDVniq8m++0DejSRDS8lyTUV2IZcAvPYAAAAAgEshRAcAQHnlPbh5aMnEaw8AAAAAuBi2WQEAAAAAAAAA4AIhOgAAAAAAAAAALhCiAwAAAAAAAADgAiE6AAAAAAAAAAAuEKIDAAAAAAAAAOACIToAAAAAAAAAAC4QogMAAAAAAAAA4AIhOgAAAAAAAAAALhCiAwAAAAAAAADggo+3F1BcmKYpSUpPT5fFwu8eAAAAAAAAAKCoSE9Pl3Q2x3UnQvQCOnLkiCSpcuXKXl4JAAAAAAAAAOBCMjIyFBYW5tYxCdELKCIiQpK0b98+t78IKNqaNGmi9evXe3sZ8AJe+5KJ173k4rUvmXjdSy5e+5KJ173k4rUvuXjtSyZe95LJNE01atRIMTExbh+bEL2AzpRwCQsLU2hoqJdXg8JktVp5zUsoXvuSide95OK1L5l43UsuXvuSide95OK1L7l47UsmXveSy8/PzyOluCnuDVzC4MGDvb0EeAmvfcnE615y8dqXTLzuJRevfcnE615y8dqXXLz2JROve8nlqdfeMD1Raf0qlJ6errCwMKWlpfGbLAAAAAAAAAAoIdiJXkD+/v4aM2aM/P39vb0UAAAAAAAAAEAhYSc6AAAAAAAAAAAusBMdAAAAAAAAAAAXCNGBf8gwDH3++efeXgYAAADcjH/nAQAA4EII0VEi9evXT4Zh5Hv88ccf3l4aPOjM6/7www/nOzZo0CAZhqF+/foV/sJQaNauXSur1apbbrnF20uBh/F+h5T3edCtWzdvLwNexOdAycH3+JIpJSVFAwcOVKVKleTv76/o6Gh17NhRP/zwg7eXhkKwf/9+DRgwQDExMfLz81PlypU1dOhQHTlypEDnr1q1SoZh6NixY55dKNzizL/vX375Zaf2zz//XIZheGlV8LRz8ztfX19FRUWpffv2ev/992W32wt1LYToKLFuueUWJSUlOT3i4uK8vSx4WGxsrBYsWKDMzExH26lTpzR//nxVqlTpisbOycm50uXBw95//30NGTJEa9as0b59+65oLJvNVujftPHPePL9DgAoWtz5PR7Fx5133qnNmzdr9uzZ2rVrlxYvXqzWrVvr6NGj3l4aPGz37t1q3Lixdu3apfnz5+uPP/7QW2+9pRUrVqh58+Z8DlylAgICNGHCBKWmpnp7KShEZ/K7PXv2aMmSJWrTpo2GDh2qLl26KDc3t9DWQYgudqiUVGd2Kpz7sFqt+vLLL9WoUSMFBATommuu0dixY/O9KZOSknTrrbcqMDBQcXFx+uSTT7x0FfinGjZsqEqVKmnhwoWOtoULFyo2NlYNGjRwtC1dulQ33nijSpcurTJlyqhLly76888/Hcf37NkjwzD08ccfq3Xr1goICNCcOXMK9Vrwz5w4cUIff/yxHnnkEXXp0kWzZs1yHDuzC+U///mPrrvuOgUEBKhp06basmWLo8+sWbNUunRpffXVV6pdu7b8/f21d+9eL1wJCspd7/ebb75Zjz76qNPYR44ckb+/v7799lvPXwjcokqVKpo6dapTW/369ZWQkOB4bhiG3nvvPd1xxx0KCgpS9erVtXjx4sJdKDymIJ8DKJ4u9j3+zPfvc11o1+KLL76ocuXKKSQkRA888ICeeuop1a9f3/OLx2U7duyY1qxZowkTJqhNmzaqXLmyrr/+eo0ePVqdO3eWJKWlpemhhx5SuXLlFBoaqptvvlmbN292jJGQkKD69evr7bffVmxsrIKCgnT33XezM7kYGDx4sPz8/PTNN9+oVatWqlSpkm699VYtX75cf/31l5555hlJUlZWlkaNGqXY2Fj5+/urevXqmjFjhvbs2aM2bdpIksLDw/krxWKiXbt2io6O1vjx4132+eyzz1SnTh35+/urSpUqmjRpkuPY6NGj1axZs3zn1KtXT2PGjPHImnHlzuR3FSpUUMOGDfX000/riy++0JIlSxzf8y/19V6SFi9erMaNGysgIECRkZHq3r37P1oHITpwjq+//lp9+vTRY489pu3bt+vtt9/WrFmz9NJLLzn1e+655xy7Hvr06aN7771XO3bs8NKq8U/df//9mjlzpuP5+++/r/79+zv1OXHihIYPH67169drxYoVslgsuuOOO/LtPH7yySf12GOPaceOHerYsWOhrB+X56OPPlLNmjVVs2ZN9enTRzNnzpRpmk59nnjiCb366qtav369ypUrp65duzr9hcHJkyc1fvx4vffee9q2bZvKlStX2JeBf8gd7/cHHnhA8+bNU1ZWluOcuXPnKiYmxvHDF64eY8eOVY8ePfTrr7+qU6dO6t27N7vZgCKuIN/jL2bu3Ll66aWXNGHCBG3cuFGVKlXSm2++6cEVwx1KlSqlUqVK6fPPP3f6Hn2GaZrq3LmzkpOT9d///lcbN25Uw4YN1bZtW6ev63/88Yc+/vhjffnll1q6dKk2bdqkwYMHF+al4B86evSovv76aw0aNEiBgYFOx6Kjo9W7d2999NFHMk1T9913nxYsWKDXXntNO3bs0FtvvaVSpUopNjZWn332mSRp586dSkpK0v/93/9543LwD1itVo0bN07Tpk3TgQMH8h3fuHGjevTooXvuuUdbtmxRQkKCnnvuOUfQ2rt3b/30009OG2a2bdumLVu2qHfv3oV1GXCDm2++Wdddd50WLlxYoK/3//nPf9S9e3d17txZv/zyi1asWKHGjRv/s0lNmH379jVvv/120zRNc8mSJeYNN9xghoWFmREREWbnzp3NP/74w9E3MTHRlGR+9tlnZuvWrc3AwECzXr165tq1a720elyOvn37mlar1QwODnY87rrrLvOmm24yx40b59T3ww8/NMuXL+94Lsl8+OGHnfo0bdrUfOSRRwpl7bh8Z97rhw4dMv39/c3ExERzz549ZkBAgHno0CHz9ttvN/v27XvBc1NSUkxJ5pYtW0zTPPu1YOrUqYV4BbgSLVq0cLxeOTk5ZmRkpLls2TLTNE1z5cqVpiRzwYIFjv5HjhwxAwMDzY8++sg0TdOcOXOmKcnctGlT4S8e/5g73++nTp0yIyIiHJ8Lpmma9evXNxMSEgrjUnAFzv03XuXKlc0pU6Y4Hb/uuuvMMWPGOJ5LMp999lnH8+PHj5uGYZhLliwphNXCEy7nc2DRokWFtj64x8W+x8+cOdMMCwtz6r9o0SLz3B+FmzZtag4ePNipzw033GBed911Hl03rtynn35qhoeHmwEBAWaLFi3M0aNHm5s3bzZN0zRXrFhhhoaGmqdOnXI6p2rVqubbb79tmqZpjhkzxrRareb+/fsdx5csWWJaLBYzKSmp8C4E/8iPP/540a/XkydPNiWZP/30kynJ8fXgfGd+BkhNTfXcYuE2535Pb9asmdm/f3/TNJ2/pvfq1cts376903lPPPGEWbt2bcfzevXqmS+88ILj+ejRo80mTZp4ePW4XOe+7ufr2bOnWatWrQJ9vW/evLnZu3fvK1oLO9HPU9Ddp88884xGjhypTZs2qUaNGrr33nsLtQ4PrlybNm20adMmx+O1117Txo0b9cILLzh2NZQqVUoPPvigkpKSdPLkSce5zZs3dxqrefPm7EQvRiIjI9W5c2fNnj1bM2fOVOfOnRUZGenU588//1SvXr10zTXXKDQ01FEv//wam//4N5fwip07d2rdunW65557JEk+Pj7q2bOn3n//fad+5763IyIiVLNmTaf3tp+fn+rVq1c4i4ZbuOP97u/vrz59+jg+XzZt2qTNmzfzJ79XqXPf48HBwQoJCVFKSooXVwTgYgr6Pf5SY1x//fVObec/R9F055136uDBg1q8eLE6duyoVatWqWHDhpo1a5Y2btyo48ePq0yZMk4/3yUmJjrtQq1UqZIqVqzoeN68eXPZ7Xbt3LnTG5cENzBP/yVKYmKirFarWrVq5eUVwd0mTJig2bNna/v27U7tO3bs0A033ODUdsMNN+j333+XzWaTlLcbfe7cuZLyPlfmz5/PLvRiyjRNGYZRoK/3mzZtUtu2ba9oPh93LPpqcueddzo9nzFjhsqVK6ft27crPj7e0T5y5EhHnbWxY8eqTp06+uOPP3TttdcW6npx+YKDg1WtWjWnNrvdrrFjx16wLlJAQMBFx+Nu0MVL//79HTWOX3/99XzHb7vtNsXGxurdd99VTEyM7Ha74uPjlZ2d7dQvODi4UNaLKzNjxgzl5uaqQoUKjjbTNOXr63vJm9Kc+94ODAzkvV4MueP9/sADD6h+/fo6cOCA3n//fbVt21aVK1cutGvAlbNYLPnKO1zohtC+vr5Ozw3D4CbCV4mCfg6geLnU9/iCvu7nf38//xwUXQEBAWrfvr3at2+v559/Xg888IDGjBmjQYMGqXz58lq1alW+c86vk3+uM58L/Juv6KpWrZoMw9D27dsveH+73377TeHh4QoKCir8xaFQtGzZUh07dtTTTz/ttLHlTKh6rvO/nvfq1UtPPfWUfv75Z2VmZmr//v2OX8SieNmxY4fi4uJkt9sv+fX+/NJPl4Od6Ocp6O7Tc3cplS9fXpLYpXQVaNiwoXbu3Klq1arle1gsZ98uP/74o9N5P/74I79AKWZuueUWZWdnKzs7O18t8yNHjmjHjh169tln1bZtW9WqVYu7fxdjubm5+uCDDzRp0iSnvz7ZvHmzKleu7NiFIDm/t1NTU7Vr1y7e21cBd7zf69atq8aNG+vdd9/VvHnz8tVVR9FXtmxZJSUlOZ6np6crMTHRiytCYeNz4OpTkO/xZcuWVUZGhk6cOOE4b9OmTU7j1KxZU+vWrXNq27BhQ2FcAjygdu3aOnHihBo2bKjk5GT5+Pjk+9nu3L9K27dvnw4ePOh4/sMPP8hisahGjRreWD4KoEyZMmrfvr3eeOMNZWZmOh1LTk7W3Llz1bNnT9WtW1d2u12rV6++4Dh+fn6S5NihjOLl5Zdf1pdffqm1a9c62mrXrq01a9Y49Vu7dq1q1Kghq9UqSapYsaJatmypuXPnau7cuWrXrp2ioqIKde24ct9++622bNmiO++8s0Bf7+vVq6cVK1Zc0ZzsRD9PQXefnrtL6cxvudilVPw9//zz6tKli2JjY3X33XfLYrHo119/1ZYtW/Tiiy86+n3yySdq3LixbrzxRs2dO1fr1q3TjBkzvLhy/FNWq9VRpuPMN9MzwsPDVaZMGb3zzjsqX7689u3bp6eeesoby4QbfPXVV0pNTdWAAQMUFhbmdOyuu+7SjBkzNGXKFEnSCy+8oDJlyigqKkrPPPOMIiMjL7i7BcWLu97vDzzwgB599FEFBQXpjjvu8Pi64V4333yzZs2apdtuu03h4eF67rnn8n0+4OrG58DVpyDf41esWKGgoCA9/fTTGjJkiNatW+e4wdwZQ4YM0YMPPqjGjRurRYsW+uijj/Trr7/qmmuuKcSrwT915MgR3X333erfv7/q1aunkJAQbdiwQRMnTtTtt9+udu3aqXnz5urWrZsmTJigmjVr6uDBg/rvf/+rbt26OcoyBgQEqG/fvnr11VeVnp6uxx57TD169FB0dLSXrxAXM336dLVo0UIdO3bUiy++qLi4OG3btk1PPPGEKlSooJdeekkRERHq27ev+vfvr9dee03XXXed9u7dq5SUFPXo0UOVK1eWYRj66quv1KlTJwUGBqpUqVLevjQUUN26ddW7d29NmzbN0TZixAg1adJE//73v9WzZ0/98MMPmj59ut544w2nc3v37q2EhARlZ2c7fhZE0ZWVlaXk5GTZbDb9/fffWrp0qcaPH68uXbrovvvuk8ViueTX+zFjxqht27aqWrWq7rnnHuXm5mrJkiUaNWpUgdfBTvRzsPsUHTt21FdffaVly5apSZMmatasmSZPnpzvT/bHjh2rBQsWqF69epo9e7bmzp2r2rVre2nVuFyhoaEKDQ3N126xWLRgwQJt3LhR8fHxevzxx/XKK694YYVwhxkzZqhdu3b5friW8kp4bdq0ST///LOkvN0MQ4cOVaNGjZSUlKTFixc7dqigeHPH+/3ee++Vj4+PevXqdckSXyga7Ha7fHzy9oyMHj1aLVu2VJcuXdSpUyd169ZNVatW9fIK4Wl8DlzdCvI9fs+ePZozZ47++9//qm7dupo/f74SEhKc+vbu3VujR4/WyJEj1bBhQyUmJqpfv358rS/iSpUqpaZNm2rKlClq2bKl4uPj9dxzz+nBBx/U9OnTZRiG/vvf/6ply5bq37+/atSooXvuuUd79uxx2nVarVo1de/eXZ06dVKHDh0UHx+fL3BD0VO9enVt2LBBVatWVc+ePVW1alU99NBDatOmjX744QdFRERIkt58803dddddGjRokK699lo9+OCDjr9MqVChgsaOHaunnnpKUVFRjvJ/KD7+/e9/O5VradiwoT7++GMtWLBA8fHxev755/XCCy/ku5fR3XffrSNHjujkyZNsmioGli5dqvLly6tKlSq65ZZbtHLlSr322mv64osvZLVaC/T1vnXr1vrkk0+0ePFi1a9fXzfffLN++umnf7QOw6TYm/r166djx45p4cKFKleunG699VaNGTPGsRtt/fr1WrRokbp166Y9e/YoLi5Ov/zyi+rXry9JOnbsmMLDw7Vy5Uq1bt3aq9cCAPjnVq1apTZt2ig1NfWiNTJRsu3fv19VqlTR+vXr1bBhQ28vBwVwyy23qFq1apo+fbq3lwIv4XMAl6t9+/aKjo7Whx9+6O2lwIMSEhL0+eef5yvxAwDA+SjnorM7VM7sRnvssccUHx+vmjVr6rXXXiMYBwCgBMvJyVFSUpKeeuopNWvWjAC9GEhNTdXatWu1atUqPfzww95eDryAzwH8EydPntRbb72ljh07ymq1av78+Vq+fLmWLVvm7aUBAIAighBdeTcErVatmiSpXbt22r59u9PxczfrV6lSJd+dfUuXLs3d2wEAuEp9//33atOmjWrUqKFPP/3U28tBAfTv31/r16/XiBEjdPvtt3t7OfACPgfwT5z5M/AXX3xRWVlZqlmzpj777DO1a9fO20sDAABFRIku53Jmh8qdd96pBQsWUAcJAAAAAAAAAOCkRO9EZ4cKAAAAAAAAAOBiSvROdAAAAAAAAAAALsbi7QUAAAAAAAAAAFBUEaIDAAAAAAAAAOACIToAAAAAAAAAAC6UmBB9/PjxatKkiUJCQlSuXDl169ZNO3fudOpjmqYSEhIUExOjwMBAtW7dWtu2bXMcP3r0qIYMGaKaNWsqKChIlSpV0mOPPaa0tDSncbp27apKlSopICBA5cuX17/+9S8dPHiwUK4TAAAAAAAAAOA+JSZEX716tQYPHqwff/xRy5YtU25urjp06KATJ044+kycOFGTJ0/W9OnTtX79ekVHR6t9+/bKyMiQJB08eFAHDx7Uq6++qi1btmjWrFlaunSpBgwY4DRXmzZt9PHHH2vnzp367LPP9Oeff+quu+4q1OsFAAAAAAAAAFw5wzRN09uL8IZDhw6pXLlyWr16tVq2bCnTNBUTE6Nhw4bpySeflCRlZWUpKipKEyZM0MCBAy84zieffKI+ffroxIkT8vHxuWCfxYsXq1u3bsrKypKvr6/HrgkAAAAAAAAA4F4lZif6+c6UYImIiJAkJSYmKjk5WR06dHD08ff3V6tWrbR27dqLjhMaGuoyQD969Kjmzp2rFi1aEKADAAAAAAAAQDFTIkN00zQ1fPhw3XjjjYqPj5ckJScnS5KioqKc+kZFRTmOne/IkSP697//fcFd6k8++aSCg4NVpkwZ7du3T1988YWbrwIAAAAAAAAA4GklMkR/9NFH9euvv2r+/Pn5jhmG4fTcNM18bZKUnp6uzp07q3bt2hozZky+40888YR++eUXffPNN7JarbrvvvtUQivnAAAAAAAAAECxdeEaJFexIUOGaPHixfruu+9UsWJFR3t0dLSkvB3p5cuXd7SnpKTk252ekZGhW265RaVKldKiRYsuWKYlMjJSkZGRqlGjhmrVqqXY2Fj9+OOPat68uYeuDAAAAAAAAADgbiVmJ7ppmnr00Ue1cOFCffvtt4qLi3M6HhcXp+joaC1btszRlp2drdWrV6tFixaOtvT0dHXo0EF+fn5avHixAgICCjS3lHejUgAAAAAAAABA8VFidqIPHjxY8+bN0xdffKGQkBBHnfOwsDAFBgbKMAwNGzZM48aNU/Xq1VW9enWNGzdOQUFB6tWrl6S8HegdOnTQyZMnNWfOHKWnpys9PV2SVLZsWVmtVq1bt07r1q3TjTfeqPDwcO3evVvPP/+8qlatyi50AAAAAAAAAChmDLOEFOq+UF1zSZo5c6b69esnKW/H+NixY/X2228rNTVVTZs21euvv+64+eiqVavUpk2bC46TmJioKlWqaMuWLRo6dKg2b96sEydOqHz58rrlllv07LPPqkKFCh65NgAAAAAAAACAZ5SYEB0AAAAAAAAAgH+qxNREBwAAAAAAAADgnyJEBwAAAAAAAADABUJ0AAAAAAAAAABcIEQHAAAAAAAAAMAFQnQAAAAAAAAAAFwgRAcAAAAAAAAAwAVCdAAAAAAAAAAAXCBEBwAAAFBghmHo888/9/YyAAAAgEJDiA4AAAAUA/369ZNhGHr44YfzHRs0aJAMw1C/fv3cNl9CQoLq16/vtvEAAACA4ooQHQAAACgmYmNjtWDBAmVmZjraTp06pfnz56tSpUpeXBkAAABw9SJEBwAAAIqJhg0bqlKlSlq4cKGjbeHChYqNjVWDBg0cbVlZWXrsscdUrlw5BQQE6MYbb9T69esdx1etWiXDMLRixQo1btxYQUFBatGihXbu3ClJmjVrlsaOHavNmzfLMAwZhqFZs2Y5zj98+LDuuOMOBQUFqXr16lq8eLHnLx4AAADwEkJ0AAAAoBi5//77NXPmTMfz999/X/3793fqM2rUKH322WeaPXu2fv75Z1WrVk0dO3bU0aNHnfo988wzmjRpkjZs2CAfHx/HOD179tSIESNUp04dJSUlKSkpST179nScN3bsWPXo0UO//vqrOnXqpN69e+cbGwAAALhaEKIDAAAAxci//vUvrVmzRnv27NHevXv1/fffq0+fPo7jJ06c0JtvvqlXXnlFt956q2rXrq13331XgYGBmjFjhtNYL730klq1aqXatWvrqaee0tq1a3Xq1CkFBgaqVKlS8vHxUXR0tKKjoxUYGOg4r1+/frr33ntVrVo1jRs3TidOnNC6desK7f8BAAAAUJh8vL0AAAAAAAUXGRmpzp07a/bs2TJNU507d1ZkZKTj+J9//qmcnBzdcMMNjjZfX19df/312rFjh9NY9erVc3xcvnx5SVJKSsol66ufe15wcLBCQkKUkpJyRdcFAAAAFFWE6AAAAEAx079/fz366KOSpNdff93pmGmakiTDMPK1n9/m6+vr+PjMMbvdfsn5zz3vzLkFOQ8AAAAojijnAgAAABQzt9xyi7Kzs5Wdna2OHTs6HatWrZr8/Py0Zs0aR1tOTo42bNigWrVqFXgOPz8/2Ww2t60ZAAAAKK7YiQ4AAAAUM1ar1VGaxWq1Oh0LDg7WI488oieeeEIRERGqVKmSJk6cqJMnT2rAgAEFnqNKlSpKTEzUpk2bVLFiRYWEhMjf39+t1wEAAAAUB4ToAAAAQDEUGhrq8tjLL78su92uf/3rX8rIyFDjxo319ddfKzw8vMDj33nnnVq4cKHatGmjY8eOaebMmerXr58bVg4AAAAUL4Z5pmgiAAAAAAAAAABwQk10AAAAAAAAAABcIEQHAAAAAAAAAMAFQnQAAAAAAAAAAFwgRAcAAAAAAAAAwAVCdAAAAAAAAAAAXCBEBwAAAAAAAADABUJ0AAAAAAAAAABcIEQHAAAAAAAAAMAFQnQAAAAAAAAAAFwgRAcAAAAAAAAAwAVCdAAAAAAAAAAAXCBEBwAAAAAAAADABUJ0AAAAAAAAAABcIEQHAAAAAAAAAMAFQnQAAAAAAAAAAFwgRAcAAAAAAAAAwAVCdAAAAAAAAAAAXCBEBwAAgNe89tprMgxD8fHxlz3GwYMHlZCQoE2bNuU7lpCQIMMwLmvcKlWqqF+/fgWa50qcWePhw4fdOu75+vXrJ8MwHI/g4GBVqVJFXbt21cyZM5WVleXR+d1hxYoVaty4sYKDg2UYhj7//PN8fQ4dOiSLxaJHHnkk37GhQ4fKMAyNHj0637EBAwbIarUqNTXV7etetWqVDMPQp59+6vaxAQAA4Hk+3l4AAAAASq73339fkrRt2zb99NNPatq06T8e4+DBgxo7dqyqVKmi+vXrOx174IEHdMstt1zW2hYtWqTQ0NACzVNcBAYG6ttvv5UkZWZmav/+/VqyZIkefPBBTZo0SUuXLlXFihW9vMoLM01TPXr0UI0aNbR48WIFBwerZs2a+fqVLVtWderU0cqVK/MdW7VqlYKDg10eq1+/vsLDwz2yfgAAABRf7EQHAACAV2zYsEGbN29W586dJUkzZsxw+xwVK1ZUs2bNLuvcBg0aqGrVqm5ekXdZLBY1a9ZMzZo1U5s2bXTfffdp/vz5+u9//6tdu3bprrvu8vYSXTp48KCOHj2qO+64Q23btlWzZs1cBt5t2rTRzp07lZyc7Gg7evSotmzZokceeUQbN25URkaG49iBAwe0e/dutWnTxuPXAQAAgOKHEB0AAABecSY0f/nll9WiRQstWLBAJ0+ezNfvr7/+0kMPPaTY2Fj5+fkpJiZGd911l/7++2+tWrVKTZo0kSTdf//9jlIlCQkJkvKXc+nWrZsqV64su92eb56mTZuqYcOGjufnlnO52DwffvihDMPQDz/8kG/MF154Qb6+vjp48OAl/3/s379f3bt3V2hoqMLCwtSnTx8dOnTIcXzAgAGKiIi44P+jm2++WXXq1LnkHK506NBBDz74oH766Sd99913jvaPPvpIHTp0UPny5RUYGKhatWrpqaee0okTJxx93HH9a9asUdu2bRUSEqKgoCC1aNFC//nPfxzHExISHDvkn3zySRmGoSpVqrgc70wYvmrVKkfb6tWr5ePjo5EjR0qS/ve//zmOndmZfm6Ivnz5crVt21ahoaEKCgrSDTfcoBUrVuSb6/fff1evXr1Urlw5+fv7q1atWnr99dcver2SlJ6ero4dOyoqKkrr1q27ZH8AAAB4DyE6AAAACl1mZqbmz5+vJk2aKD4+Xv3791dGRoY++eQTp35//fWXmjRpokWLFmn48OFasmSJpk6dqrCwMKWmpqphw4aaOXOmJOnZZ5/VDz/8oB9++EEPPPDABeft37+/9u3b5yhpcsZvv/2mdevW6f7777/geRebp2fPnoqOjs4XnObm5urtt9/WHXfcoZiYmEv+P7njjjtUrVo1ffrpp0pISNDnn3+ujh07KicnR1JePe/U1FTNmzfP6bzt27dr5cqVGjx48CXnuJiuXbtKklOI/vvvv6tTp06aMWOGli5dqmHDhunjjz/Wbbfd5uhzpde/evVq3XzzzUpLS9OMGTM0f/58hYSE6LbbbtNHH30kKa8sz8KFCyVJQ4YM0Q8//KBFixa5HLNVq1ayWCxOZVtWrlypxo0bKyoqSo0aNXIK2FeuXCmr1aqbbrpJkjRnzhx16NBBoaGhmj17tj7++GNFRESoY8eOTkH69u3b1aRJE23dulWTJk3SV199pc6dO+uxxx7T2LFjXa7vwIEDuvHGG7V371798MMPuv766132BQAAQBFgAgAAAIXsgw8+MCWZb731lmmappmRkWGWKlXKvOmmm5z69e/f3/T19TW3b9/ucqz169ebksyZM2fmOzZmzBjz3H/y5uTkmFFRUWavXr2c+o0aNcr08/MzDx8+7GirXLmy2bdv3wLP4+fnZ/7999+Oto8++siUZK5evdrl2s9d4+OPP+7UPnfuXFOSOWfOHEdbq1atzPr16zv1e+SRR8zQ0FAzIyPjovP07dvXDA4Odnl8x44dpiTzkUceueBxu91u5uTkmKtXrzYlmZs3b3a6hsu9/mbNmpnlypVzWn9ubq4ZHx9vVqxY0bTb7aZpmmZiYqIpyXzllVcuOt4Z9evXN2vUqOF4XrduXfOpp54yTTPv9W7cuLHjWFxcnHn99debpmmaJ06cMCMiIszbbrvNaTybzWZed911jn6maZodO3Y0K1asaKalpTn1ffTRR82AgADz6NGjpmma5sqVK01J5ieffGL+8ssvZkxMjHnTTTeZR44cKdC1AAAAwLvYiQ4AAIBCN2PGDAUGBuqee+6RJJUqVUp33323/ve//+n333939FuyZInatGmjWrVquWVeHx8f9enTRwsXLlRaWpokyWaz6cMPP9Ttt9+uMmXKXNa4jzzyiCTp3XffdbRNnz5ddevWVcuWLQs0Ru/evZ2e9+jRQz4+Pk67qYcOHapNmzbp+++/l5RXEuTDDz9U3759VapUqcta+xmmaeZr2717t3r16qXo6GhZrVb5+vqqVatWkqQdO3Y4+l3u9Z84cUI//fST7rrrLqf1W61W/etf/9KBAwe0c+fOy7qeNm3aaNeuXTp48KCOHDmirVu3qnXr1pLydqr/8ssvSktL0759+5SYmOgo5bJ27VodPXpUffv2VW5uruNht9t1yy23aP369Tpx4oROnTqlFStW6I477lBQUJBT306dOunUqVP68ccfndb09ddf66abblLLli21bNkyRUREXNa1AQAAoHARogMAAKBQ/fHHH/ruu+/UuXNnmaapY8eO6dixY46bWr7//vuOvocOHXLUwnaX/v3769SpU1qwYIGkvGAzKSnJZSmXgoiKilLPnj319ttvy2az6ddff9X//vc/PfroowUeIzo62um5j4+PypQpoyNHjjjabr/9dlWpUsVROmXWrFk6ceLEFZdykaS9e/dKkqP0yvHjx3XTTTfpp59+0osvvqhVq1Zp/fr1jrIqmZmZjnMv9/pTU1NlmqbKly+f79iZdZx7/f/EuXXRV61aJavVqhtuuEGSdOONN0rKq4t+fj30v//+W5J01113ydfX1+kxYcIEmaapo0eP6siRI8rNzdW0adPy9evUqZMk6fDhw05r+vzzz5WZmalHHnlE/v7+l3VdAAAAKHw+3l4AAAAASpb3339fpmnq008/1aeffprv+OzZs/Xiiy/KarWqbNmyOnDggFvnr127tq6//nrNnDlTAwcO1MyZMxUTE6MOHTpc0bhDhw7Vhx9+qC+++EJLly5V6dKl8+0uv5jk5GRVqFDB8Tw3N1dHjhxx2h1vsVg0ePBgPf3005o0aZLeeOMNtW3bVjVr1ryitUvS4sWLJcmxW/vbb7/VwYMHtWrVKsfuc0k6duzYBc+/nOsPDw+XxWJRUlJSvmNnbkYaGRl5GVcjtWzZUlarVatWrZK/v78aNmzo2O0eGhqq+vXra+XKlTp69Kh8fHwcAfuZ+aZNm6ZmzZpdcOyoqCjl5uY6dsy7+iVGXFyc0/MpU6boo48+0q233qpFixZd8eccAAAACgchOgAAAAqNzWbT7NmzVbVqVb333nv5jn/11VeaNGmSlixZoi5duujWW2/Vhx9+qJ07d7oMis/s6D13Z/Sl3H///XrkkUe0Zs0affnllxo+fLisVutFz7nUPI0aNVKLFi00YcIEbd26VQ899JCCg4MLvKa5c+eqUaNGjucff/yxcnNzHaH2GQ888IASEhLUu3dv7dy5UxMmTCjwHK4sW7ZM7733nlq0aOHYpW0YhiTl2zH99ttvX3CMy7n+4OBgNW3aVAsXLtSrr76qwMBASZLdbtecOXNUsWJF1ahR47KuKSwsTA0aNHCE6Gd2h5/RqlUrrVy5Uqmpqbr++usdAfsNN9yg0qVLa/v27RfdSe/n56c2bdrol19+Ub169eTn53fJNQUEBGjhwoXq06ePunbtqo8++ki33377ZV0fAAAACg8hOgAAAArNkiVLdPDgQU2YMCFfOCxJ8fHxmj59umbMmKEuXbrohRde0JIlS9SyZUs9/fTTqlu3ro4dO6alS5dq+PDhuvbaa1W1alUFBgZq7ty5qlWrlkqVKqWYmBhHOZALuffeezV8+HDde++9ysrKUr9+/S659oLMM3ToUPXs2VOGYWjQoEH/6P/NwoUL5ePjo/bt22vbtm167rnndN1116lHjx5O/UqXLq377rtPb775pipXrqzbbrutwHPY7XZHne6srCzt27dPS5Ys0ccff6xatWrp448/dvRt0aKFwsPD9fDDD2vMmDHy9fXV3LlztXnzZpfjX871jx8/Xu3bt1ebNm00cuRI+fn56Y033tDWrVs1f/58R5h/Odq0aaNXXnlFhmHk+2VDq1atNGXKFJmm6bRjvlSpUpo2bZr69u2ro0eP6q677lK5cuV06NAhbd68WYcOHdKbb74pSfq///s/3Xjjjbrpppv0yCOPqEqVKsrIyNAff/yhL7/8Ut9++22+Nfn6+mr+/Pl64IEHdNddd+mDDz7Qvffee9nXCAAAAM+jJjoAAAAKzYwZM+Tn5+ey/nhkZKTuuOMOffXVV/r7779VoUIFrVu3Tl26dNHLL7+sW265RUOGDFFaWprjpoxBQUF6//33deTIEXXo0EFNmjTRO++8c9F1hIWF6Y477tCBAwd0ww03FGi3c0Hm6datm/z9/dWxY0dVr169gP9X8ixcuFC//fabunfvrueff1633XabvvnmmwvucO7Zs6ekvBt6WiwF/yd9ZmammjdvrubNm+vWW2/Vs88+q+PHj+vdd9/Vxo0bncrJlClTRv/5z38UFBSkPn36qH///ipVqpQ++ugjl+NfzvW3atVK3377rYKDg9WvXz/dc889SktL0+LFix3XebnO1Dm3WCyOHfZn3HTTTY6A/vxf6PTp00crV67U8ePHNXDgQLVr105Dhw7Vzz//rLZt2zr61a5dWz///LPi4+P17LPPqkOHDhowYIA+/fRTp37ns1gsmjFjhgYNGqQ+ffpc8K8yAAAAUHQYpmma3l4EAAAAcDX48ssv1bVrV/3nP//JVz7EnUaMGKE333xT+/fvd6qZ7m2Fdf0AAABAYSJEBwAAAK7Q9u3btXfvXg0dOlTBwcH6+eefr6gMiSs//vijdu3apYEDB2rgwIGaOnWq2+e4HIV1/QAAAIA3EKIDAAAAV6h169b6/vvv1bBhQ82ePVvXXnutR+YxDENBQUHq1KmTZs6c6bgZprcV1vUDAAAA3kCIDgAAAAAAAACAC9xYFAAAAAAAAAAAFwjRAQAAAAAAAABwgRAdAAAAAAAAAAAXfLy9gOLCbrfr4MGDCgkJkWEY3l4OAAAAAAAAAOA00zSVkZGhmJgYWSzu3TtOiF5ABw8eVGxsrLeXAQAAAAAAAABwYf/+/apYsaJbxyREL6CQkBBJeS9CaGiol1cDAAAAAAAAADgjPT1dsbGxjhzXnQjRC+hMCZfQ0FBCdAAAAAAAAAAogjxRipsbiwIAAAAAAAAA4AIhOgAAAAAAAAAALhCiAwAAAAAAAADgAiE6AAAAAAAAAAAuEKIDAAAAAAAAAOACIToAAAAAAAAAAC4QogMAAAAAAAAA4AIhOgAAAAAAAAAALhCiAwAAAAAAAADgAiE6AAAAAAAAAAAuEKIDAAAAAAAAAOACIToAAAAAAAAAAC4QogMAAAAAAAAA4AIhOgAAAAAAAAAALhCiAwAAAAAAAADggo+3FwAAAIquhTuTvL0EnKN7zfLeXgIAAAAAlDjsRAcAAAAAAAAAwAVCdAAAAAAAAAAAXKCcCwAAAADALSgDVnRQAgwAAPdhJzoAAAAAAAAAAC4QogMAAAAAAAAA4AIhOgAAAAAAAAAALhCiAwAAAAAAAADgAiE6AAAAAAAAAAAuEKIDAAAAAAAAAOACIToAAAAAAAAAAC4QogMAAAAAAAAA4AIhOgAAAAAAAAAALhTpED03N1fPPvus4uLiFBgYqGuuuUYvvPCC7Ha7o49pmkpISFBMTIwCAwPVunVrbdu2zWmcrKwsDRkyRJGRkQoODlbXrl114MCBwr4cAAAAAAAAAEAxU6RD9AkTJuitt97S9OnTtWPHDk2cOFGvvPKKpk2b5ugzceJETZ48WdOnT9f69esVHR2t9u3bKyMjw9Fn2LBhWrRokRYsWKA1a9bo+PHj6tKli2w2mzcuCwAAAAAAAABQTPh4ewEX88MPP+j2229X586dJUlVqlTR/PnztWHDBkl5u9CnTp2qZ555Rt27d5ckzZ49W1FRUZo3b54GDhyotLQ0zZgxQx9++KHatWsnSZozZ45iY2O1fPlydezY8YJzZ2VlKSsry/E8PT3dk5cKAAAAAAAAACiCivRO9BtvvFErVqzQrl27JEmbN2/WmjVr1KlTJ0lSYmKikpOT1aFDB8c5/v7+atWqldauXStJ2rhxo3Jycpz6xMTEKD4+3tHnQsaPH6+wsDDHIzY21hOXCAAAAAAAAAAowor0TvQnn3xSaWlpuvbaa2W1WmWz2fTSSy/p3nvvlSQlJydLkqKiopzOi4qK0t69ex19/Pz8FB4enq/PmfMvZPTo0Ro+fLjjeXp6OkE6AAAAAAAAAJQwRTpE/+ijjzRnzhzNmzdPderU0aZNmzRs2DDFxMSob9++jn6GYTidZ5pmvrbzXaqPv7+//P39r+wCAAAAAAAAAADFWpEO0Z944gk99dRTuueeeyRJdevW1d69ezV+/Hj17dtX0dHRkvJ2m5cvX95xXkpKimN3enR0tLKzs5Wamuq0Gz0lJUUtWrQoxKsBAAAAAAAAABQ3Rbom+smTJ2WxOC/RarXKbrdLkuLi4hQdHa1ly5Y5jmdnZ2v16tWOgLxRo0by9fV16pOUlKStW7cSogMAAAAAAAAALqpI70S/7bbb9NJLL6lSpUqqU6eOfvnlF02ePFn9+/eXlFfGZdiwYRo3bpyqV6+u6tWra9y4cQoKClKvXr0kSWFhYRowYIBGjBihMmXKKCIiQiNHjlTdunXVrl07b14eAAAAAAAAAKCIK9Ih+rRp0/Tcc89p0KBBSklJUUxMjAYOHKjnn3/e0WfUqFHKzMzUoEGDlJqaqqZNm+qbb75RSEiIo8+UKVPk4+OjHj16KDMzU23bttWsWbNktVq9cVkAiqCFO5O8vQSco3vN8pfuBAAAAAAAUAgM0zRNby+iOEhPT1dYWJjS0tIUGhrq7eUAcDNC9KKFEL3o4L1RtPDeAFDU8X2j6OB7BgCgpPFkfluka6IDAAAAAAAAAOBNhOgAAAAAAAAAALhAiA4AAAAAAAAAgAuE6AAAAAAAAAAAuECIDgAAAAAAAACAC4ToAAAAAAAAAAC4QIgOAAAAAAAAAIALhOgAAAAAAAAAALhAiA4AAAAAAAAAgAuE6AAAAAAAAAAAuECIDgAAAAAAAACAC4ToAAAAAAAAAAC4QIgOAAAAAAAAAIALhOgAAAAAAAAAALhAiA4AAAAAAAAAgAuE6AAAAAAAAAAAuECIDgAAAAAAAACAC4ToAAAAAAAAAAC4QIgOAAAAAAAAAIALhOgAAAAAAAAAALhAiA4AAAAAAAAAgAuE6AAAAAAAAAAAuODj7QWgcC3cmeTtJeAc3WuW9/YSAAAAAAAAAFwEO9EBAAAAAAAAAHCBEB0AAAAAAAAAABcI0QEAAAAAAAAAcIEQHQAAAAAAAAAAFwjRAQAAAAAAAABwgRAdAAAAAAAAAAAXCNEBAAAAAAAAAHCBEB0AAAAAAAAAABd8vL0AAAAAAAAAACXLwp1J3l4CztG9ZnlvL6FIK/I70atUqSLDMPI9Bg8eLEkyTVMJCQmKiYlRYGCgWrdurW3btjmNkZWVpSFDhigyMlLBwcHq2rWrDhw44I3LAQAAAAAAAAAUI0U+RF+/fr2SkpIcj2XLlkmS7r77bknSxIkTNXnyZE2fPl3r169XdHS02rdvr4yMDMcYw4YN06JFi7RgwQKtWbNGx48fV5cuXWSz2bxyTQAAAAAAAACA4qHIh+hly5ZVdHS04/HVV1+patWqatWqlUzT1NSpU/XMM8+oe/fuio+P1+zZs3Xy5EnNmzdPkpSWlqYZM2Zo0qRJateunRo0aKA5c+Zoy5YtWr58uZevDgAAAAAAAABQlBX5EP1c2dnZmjNnjvr37y/DMJSYmKjk5GR16NDB0cff31+tWrXS2rVrJUkbN25UTk6OU5+YmBjFx8c7+lxIVlaW0tPTnR4AAAAAAAAAgJKlWIXon3/+uY4dO6Z+/fpJkpKTkyVJUVFRTv2ioqIcx5KTk+Xn56fw8HCXfS5k/PjxCgsLczxiY2PdeCUAAAAAAAAAgOKgWIXoM2bM0K233qqYmBindsMwnJ6bppmv7XyX6jN69GilpaU5Hvv377/8hQMAAAAAAAAAiqViE6Lv3btXy5cv1wMPPOBoi46OlqR8O8pTUlIcu9Ojo6OVnZ2t1NRUl30uxN/fX6GhoU4PAAAAAAAAAEDJUmxC9JkzZ6pcuXLq3Lmzoy0uLk7R0dFatmyZoy07O1urV69WixYtJEmNGjWSr6+vU5+kpCRt3brV0QcAAAAAAAAAgAvx8fYCCsJut2vmzJnq27evfHzOLtkwDA0bNkzjxo1T9erVVb16dY0bN05BQUHq1auXJCksLEwDBgzQiBEjVKZMGUVERGjkyJGqW7eu2rVr561LAgAAAAAAAAAUA8UiRF++fLn27dun/v375zs2atQoZWZmatCgQUpNTVXTpk31zTffKCQkxNFnypQp8vHxUY8ePZSZmam2bdtq1qxZslqthXkZAAAAAAAAAIBipliE6B06dJBpmhc8ZhiGEhISlJCQ4PL8gIAATZs2TdOmTfPQCgEAAAAAAAAAV6NiUxMdAAAAAAAAAIDCRogOAAAAAAAAAIALhOgAAAAAAAAAALhAiA4AAAAAAAAAgAuE6AAAAAAAAAAAuECIDgAAAAAAAACAC4ToAAAAAAAAAAC4QIgOAAAAAAAAAIALhOgAAAAAAAAAALhAiA4AAAAAAAAAgAuE6AAAAAAAAAAAuECIDgAAAAAAAACAC4ToAAAAAAAAAAC4QIgOAAAAAAAAAIALhOgAAAAAAAAAALhAiA4AAAAAAAAAgAuE6AAAAAAAAAAAuECIDgAAAAAAAACAC4ToAAAAAAAAAAC4QIgOAAAAAAAAAIALhOgAAAAAAAAAALhAiA4AAAAAAAAAgAuE6AAAAAAAAAAAuECIDgAAAAAAAACAC4ToAAAAAAAAAAC4QIgOAAAAAAAAAIALhOgAAAAAAAAAALhAiA4AAAAAAAAAgAuE6AAAAAAAAAAAuECIDgAAAAAAAACAC4ToAAAAAAAAAAC4QIgOAAAAAAAAAIALRT5E/+uvv9SnTx+VKVNGQUFBql+/vjZu3Og4bpqmEhISFBMTo8DAQLVu3Vrbtm1zGiMrK0tDhgxRZGSkgoOD1bVrVx04cKCwLwUAAAAAAAAAUMwU6RA9NTVVN9xwg3x9fbVkyRJt375dkyZNUunSpR19Jk6cqMmTJ2v69Olav369oqOj1b59e2VkZDj6DBs2TIsWLdKCBQu0Zs0aHT9+XF26dJHNZvPCVQEAAAAAAAAAigsfby/gYiZMmKDY2FjNnDnT0ValShXHx6ZpaurUqXrmmWfUvXt3SdLs2bMVFRWlefPmaeDAgUpLS9OMGTP04Ycfql27dpKkOXPmKDY2VsuXL1fHjh0L9ZoAAAAAAAAAAMVHkd6JvnjxYjVu3Fh33323ypUrpwYNGujdd991HE9MTFRycrI6dOjgaPP391erVq20du1aSdLGjRuVk5Pj1CcmJkbx8fGOPheSlZWl9PR0pwcAAAAAAAAAoGTxSIj+888/a8uWLY7nX3zxhbp166ann35a2dnZBR5n9+7devPNN1W9enV9/fXXevjhh/XYY4/pgw8+kCQlJydLkqKiopzOi4qKchxLTk6Wn5+fwsPDXfa5kPHjxyssLMzxiI2NLfC6AQAAAAAAAABXB4+E6AMHDtSuXbsk5QXh99xzj4KCgvTJJ59o1KhRBR7HbrerYcOGGjdunBo0aKCBAwfqwQcf1JtvvunUzzAMp+emaeZrO9+l+owePVppaWmOx/79+wu8bgAAAAAAAADA1cEjIfquXbtUv359SdInn3yili1bat68eZo1a5Y+++yzAo9Tvnx51a5d26mtVq1a2rdvnyQpOjpakvLtKE9JSXHsTo+OjlZ2drZSU1Nd9rkQf39/hYaGOj0AAAAAAAAAACWLR0J00zRlt9slScuXL1enTp0kSbGxsTp8+HCBx7nhhhu0c+dOp7Zdu3apcuXKkqS4uDhFR0dr2bJljuPZ2dlavXq1WrRoIUlq1KiRfH19nfokJSVp69atjj4AAAAAAAAAAFyIjycGbdy4sV588UW1a9dOq1evdpRfSUxMvOju7/M9/vjjatGihcaNG6cePXpo3bp1euedd/TOO+9IyivjMmzYMI0bN07Vq1dX9erVNW7cOAUFBalXr16SpLCwMA0YMEAjRoxQmTJlFBERoZEjR6pu3bpq166d+y8eAAAAV72FO5O8vQSc1r1meW8vAQAAAFc5j4ToU6dOVe/evfX555/rmWeeUbVq1SRJn3766T/a/d2kSRMtWrRIo0eP1gsvvKC4uDjH2GeMGjVKmZmZGjRokFJTU9W0aVN98803CgkJcfSZMmWKfHx81KNHD2VmZqpt27aaNWuWrFar+y4aAAAAAAAAAHDVMUzTNAtrslOnTslqtcrX17ewpnSb9PR0hYWFKS0trVjXR2fXVNHCzqmig/dG0cJ7o+jgvVG08N4oOnhvFB28L4oW3htFB+8NAEUd3zOKlqvh+4Yn81uP1ESXpGPHjum9997T6NGjdfToUUnS9u3blZKS4qkpAQAAAAAAAABwK4+Uc/n111/Vtm1blS5dWnv27NGDDz6oiIgILVq0SHv37tUHH3zgiWkBAAAAAAAAAHArj+xEHz58uO6//379/vvvCggIcLTfeuut+u677zwxJQAAAAAAAAAAbueREH39+vUaOHBgvvYKFSooOTnZE1MCAAAAAAAAAOB2HgnRAwIClJ6enq99586dKlu2rCemBAAAAAAAAADA7TwSot9+++164YUXlJOTI0kyDEP79u3TU089pTvvvNMTUwIAAAAAAAAA4HYeCdFfffVVHTp0SOXKlVNmZqZatWqlatWqKSQkRC+99JInpgQAAAAAAAAAwO18PDFoaGio1qxZo2+//VY///yz7Ha7GjZsqHbt2nliOgAAAAAAAAAAPMIjIfoZN998s26++WZPTgEAAAAAAAAAgMe4LUR/7bXX9NBDDykgIECvvfbaRfs+9thj7poWAAAAAAAAAACPcVuIPmXKFPXu3VsBAQGaMmWKy36GYRCiAwAAAAAAAACKBbeF6ImJiRf8GAAAAAAAAACA4sriiUFfeOEFnTx5Ml97ZmamXnjhBU9MCQAAAAAAAACA23kkRB87dqyOHz+er/3kyZMaO3asJ6YEAAAAAAAAAMDtPBKim6YpwzDytW/evFkRERGemBIAAAAAAAAAALdzW010SQoPD5dhGDIMQzVq1HAK0m02m44fP66HH37YnVMCAAAAAAAAAOAxbg3Rp06dKtM01b9/f40dO1ZhYWGOY35+fqpSpYqaN2/uzikBAAAAAAAAAPAYt4boffv2lSTFxcWpRYsW8vX1defwAAAAAAAAAAAUKreF6Onp6QoNDZUkNWjQQJmZmcrMzLxg3zP9AAAAAAAAAAAoytwWooeHhyspKUnlypVT6dKlL3hj0TM3HLXZbO6aFgAAAAAAAAAAj3FbiP7tt98qIiLC8fGFQnQAAAAAAAAAAIoTt4XorVq1cnzcunVrdw0LAAAAAAAAAIDXWDwx6DXXXKPnnntOO3fu9MTwAAAAAAAAAAAUCo+E6I8++qiWLl2qWrVqqVGjRpo6daqSkpI8MRUAAAAAAAAAAB7jkRB9+PDhWr9+vX777Td16dJFb775pipVqqQOHTrogw8+8MSUAAAAAAAAAAC4nUdC9DNq1KihsWPHaufOnfrf//6nQ4cO6f777/fklAAAAAAAAAAAuI3bbizqyrp16zRv3jx99NFHSktL01133eXpKQEAAAAAAAAAcAuPhOi7du3S3LlzNW/ePO3Zs0dt2rTRyy+/rO7duyskJMQTUwIAAAAAAAAA4HYeCdGvvfZaNW7cWIMHD9Y999yj6OhoT0wDAAAAAAAAAIBHeSRE/+2331SjRg1PDA0AAAAAAAAAQKHxyI1FCdABAAAAAAAAAFcDt+1Ej4iI0K5duxQZGanw8HAZhuGy79GjR901LQAAAAAAAAAAHuO2EH3KlCmOm4ZOmTLloiF6QSUkJGjs2LFObVFRUUpOTpYkmaapsWPH6p133lFqaqqaNm2q119/XXXq1HH0z8rK0siRIzV//nxlZmaqbdu2euONN1SxYsUrXh8AAAAAAMClLNyZ5O0l4LTuNct7ewkAiiG3heh9+/Z1fNyvXz93Das6depo+fLljudWq9Xx8cSJEzV58mTNmjVLNWrU0Isvvqj27dtr586djkB/2LBh+vLLL7VgwQKVKVNGI0aMUJcuXbRx40ansQAAAAAAAAAAOJ9HaqJbrValpKTkaz9y5Mg/Dq59fHwUHR3teJQtW1ZS3i70qVOn6plnnlH37t0VHx+v2bNn6+TJk5o3b54kKS0tTTNmzNCkSZPUrl07NWjQQHPmzNGWLVucgnkAAAAAAAAAAC7EIyG6aZoXbM/KypKfn98/Guv3339XTEyM4uLidM8992j37t2SpMTERCUnJ6tDhw6Ovv7+/mrVqpXWrl0rSdq4caNycnKc+sTExCg+Pt7Rx5WsrCylp6c7PQAAAAAAAAAAJYvbyrlI0muvvSZJMgxD7733nkqVKuU4ZrPZ9N133+naa68t8HhNmzbVBx98oBo1aujvv//Wiy++qBYtWmjbtm2OuuhRUVFO50RFRWnv3r2SpOTkZPn5+Sk8PDxfnzPnuzJ+/Ph89dgBAAAAAAAAACWLW0P0KVOmSMrbif7WW285lW7x8/NTlSpV9NZbbxV4vFtvvdXxcd26ddW8eXNVrVpVs2fPVrNmzSQp3w1MTdO85E1NC9Jn9OjRGj58uON5enq6YmNjC7x2AAAAAAAAAEDx59YQPTExUZLUpk0bLVy4MN8O8CsVHBysunXr6vfff1e3bt0k5e02L1/+7J2VU1JSHLvTo6OjlZ2drdTUVKe1pKSkqEWLFhedy9/fX/7+/m5dPwAAAAAAAACgePFITfSVK1e6PUCX8uqU79ixQ+XLl1dcXJyio6O1bNkyx/Hs7GytXr3aEZA3atRIvr6+Tn2SkpK0devWS4boAAAAAAAAAAB4JES/66679PLLL+drf+WVV3T33XcXeJyRI0dq9erVSkxM1E8//aS77rpL6enp6tu3rwzD0LBhwzRu3DgtWrRIW7duVb9+/RQUFKRevXpJksLCwjRgwACNGDFCK1as0C+//KI+ffqobt26ateunduuFwAAAAAAAABwdXJrOZczVq9erTFjxuRrv+WWW/Tqq68WeJwDBw7o3nvv1eHDh1W2bFk1a9ZMP/74oypXrixJGjVqlDIzMzVo0CClpqaqadOm+uabbxQSEuIYY8qUKfLx8VGPHj2UmZmptm3batasWU712gEAAAAAAAAAuBCPhOjHjx+Xn59fvnZfX1+lp6cXeJwFCxZc9LhhGEpISFBCQoLLPgEBAZo2bZqmTZtW4HkBAAAAAAAAAJA8VM4lPj5eH330Ub72BQsWqHbt2p6YEgAAAAAAAAAAt/PITvTnnntOd955p/7880/dfPPNkqQVK1Zo/vz5+uSTTzwxJQAAAAAAAAAAbueREL1r1676/PPPNW7cOH366acKDAxUvXr1tHz5crVq1coTUwIAAAAAAAAA4HYeCdElqXPnzurcuXO+9k2bNql+/fqemhYAAAAAAAAAALfxSE3086WlpemNN95Qw4YN1ahRo8KYEgAAAAAAAACAK+bREP3bb79V7969Vb58eU2bNk2dOnXShg0bPDklAAAAAAAAAABu4/ZyLgcOHNCsWbP0/vvv68SJE+rRo4dycnL02WefqXbt2u6eDgAAAAAAAAAAj3HrTvROnTqpdu3a2r59u6ZNm6aDBw9q2rRp7pwCAAAAAAAAAIBC49ad6N98840ee+wxPfLII6pevbo7hwYAAAAAAAAAoNC5dSf6//73P2VkZKhx48Zq2rSppk+frkOHDrlzCgAAAAAAAAAACo1bQ/TmzZvr3XffVVJSkgYOHKgFCxaoQoUKstvtWrZsmTIyMtw5HQAAAAAAAAAAHuXWEP2MoKAg9e/fX2vWrNGWLVs0YsQIvfzyyypXrpy6du3qiSkBAAAAAAAAAHA7j4To56pZs6YmTpyoAwcOaP78+Z6eDgAAAAAAAAAAt/F4iH6G1WpVt27dtHjx4sKaEgAAAAAAAACAK1JoIToAAAAAAAAAAMUNIToAAAAAAAAAAC4QogMAAAAAAAAA4ILbQvSGDRsqNTVVkvTCCy/o5MmT7hoaAAAAAAAAAACvcFuIvmPHDp04cUKSNHbsWB0/ftxdQwMAAAAAAAAA4BU+7hqofv36uv/++3XjjTfKNE29+uqrKlWq1AX7Pv/88+6aFgAAAAAAAAAAj3FbiD5r1iyNGTNGX331lQzD0JIlS+Tjk394wzAI0QEAAAAAAAAAxYLbQvSaNWtqwYIFkiSLxaIVK1aoXLly7hoeAAAAAAAAAIBC57YQ/Vx2u90TwwIAAAAAAAAAUKg8EqJL0p9//qmpU6dqx44dMgxDtWrV0tChQ1W1alVPTQkAAAAAAAAAgFtZPDHo119/rdq1a2vdunWqV6+e4uPj9dNPP6lOnTpatmyZJ6YEAAAAAAAAAMDtPLIT/amnntLjjz+ul19+OV/7k08+qfbt23tiWgAAAAAAAAAA3MojO9F37NihAQMG5Gvv37+/tm/f7okpAQAAAAAAAABwO4+E6GXLltWmTZvytW/atEnlypXzxJQAAAAAAAAAALidR8q5PPjgg3rooYe0e/dutWjRQoZhaM2aNZowYYJGjBjhiSkBAAAAAAAAAHA7j4Tozz33nEJCQjRp0iSNHj1akhQTE6OEhAQ99thjnpgSAAAAAAAAAAC380iIbhiGHn/8cT3++OPKyMiQJIWEhHhiKgAAAAAAAAAAPMYjIfq5CM8BAAAAAAAAAMWVR24s6injx4+XYRgaNmyYo800TSUkJCgmJkaBgYFq3bq1tm3b5nReVlaWhgwZosjISAUHB6tr1646cOBAIa8eAAAAAAAAAFDcFJsQff369XrnnXdUr149p/aJEydq8uTJmj59utavX6/o6Gi1b9/eUUZGkoYNG6ZFixZpwYIFWrNmjY4fP64uXbrIZrMV9mUAAAAAAAAAAIqRYhGiHz9+XL1799a7776r8PBwR7tpmpo6daqeeeYZde/eXfHx8Zo9e7ZOnjypefPmSZLS0tI0Y8YMTZo0Se3atVODBg00Z84cbdmyRcuXL/fWJQEAAAAAAAAAigG3h+g5OTlq06aNdu3a5bYxBw8erM6dO6tdu3ZO7YmJiUpOTlaHDh0cbf7+/mrVqpXWrl0rSdq4caNycnKc+sTExCg+Pt7R50KysrKUnp7u9AAAAAAAAAAAlCxuv7Gor6+vtm7dKsMw3DLeggUL9PPPP2v9+vX5jiUnJ0uSoqKinNqjoqK0d+9eRx8/Pz+nHexn+pw5/0LGjx+vsWPHXunyAQAAAAAAAADFmEfKudx3332aMWPGFY+zf/9+DR06VHPmzFFAQIDLfucH9qZpXjLEv1Sf0aNHKy0tzfHYv3//P1s8AAAAAAAAAKDYc/tOdEnKzs7We++9p2XLlqlx48YKDg52Oj558uQCjbNx40alpKSoUaNGjjabzabvvvtO06dP186dOyXl7TYvX768o09KSopjd3p0dLSys7OVmprqtBs9JSVFLVq0cDm3v7+//P39C7ROAAAAAAAAAMDVySMh+tatW9WwYUNJylcb/Z+UeWnbtq22bNni1Hb//ffr2muv1ZNPPqlrrrlG0dHRWrZsmRo0aCApL8BfvXq1JkyYIElq1KiRfH19tWzZMvXo0UOSlJSUpK1bt2rixImXfY0AAAAAAAAAgKufR0L0lStXumWckJAQxcfHO7UFBwerTJkyjvZhw4Zp3Lhxql69uqpXr65x48YpKChIvXr1kiSFhYVpwIABGjFihMqUKaOIiAiNHDlSdevWzXejUgAAAAAAAAAAzuWREP2MP/74Q3/++adatmypwMDAAtUq/6dGjRqlzMxMDRo0SKmpqWratKm++eYbhYSEOPpMmTJFPj4+6tGjhzIzM9W2bVvNmjVLVqvVrWsBAAAAAAAAAFxdPBKiHzlyRD169NDKlStlGIZ+//13XXPNNXrggQdUunRpTZo06bLHXrVqldNzwzCUkJCghIQEl+cEBARo2rRpmjZt2mXPCwAAAAAAAAAoeSyeGPTxxx+Xr6+v9u3bp6CgIEd7z549tXTpUk9MCQAAAAAAAACA23lkJ/o333yjr7/+WhUrVnRqr169uvbu3euJKQEAAAAAAAAAcDuP7EQ/ceKE0w70Mw4fPix/f39PTAkAAAAAAAAAgNt5JERv2bKlPvjgA8dzwzBkt9v1yiuvqE2bNp6YEgAAAAAAAAAAt/NIOZdXXnlFrVu31oYNG5Sdna1Ro0Zp27ZtOnr0qL7//ntPTAkAAAAAAAAAgNt5ZCd67dq19euvv+r6669X+/btdeLECXXv3l2//PKLqlat6okpAQAAAAAAAABwO4/sRJek6OhojR071lPDAwAAAAAAAADgcR4L0VNTUzVjxgzt2LFDhmGoVq1auv/++xUREeGpKQEAAAAAAAAAcCuPlHNZvXq14uLi9Nprryk1NVVHjx7Va6+9pri4OK1evdoTUwIAAAAAAAAA4HYe2Yk+ePBg9ejRQ2+++aasVqskyWazadCgQRo8eLC2bt3qiWkBAAAAAAAAAHArj+xE//PPPzVixAhHgC5JVqtVw4cP159//umJKQEAAAAAAAAAcDuPhOgNGzbUjh078rXv2LFD9evX98SUAAAAAAAAAAC4ndvKufz666+Ojx977DENHTpUf/zxh5o1ayZJ+vHHH/X666/r5ZdfdteUAAAAAAAAAAB4lNtC9Pr168swDJmm6WgbNWpUvn69evVSz5493TUtAAAAAAAAAAAe47YQPTEx0V1DAQAAAAAAAABQJLgtRK9cubK7hgIAAAAAAAAAoEhwW4h+vr/++kvff/+9UlJSZLfbnY499thjnpoWAAAAAAAAAAC38UiIPnPmTD388MPy8/NTmTJlZBiG45hhGIToAAAAAAAAAIBiwSMh+vPPP6/nn39eo0ePlsVi8cQUAAAAAAAAAAB4nEcS7pMnT+qee+4hQAcAAAAAAAAAFGseSbkHDBigTz75xBNDAwAAAAAAAABQaDxSzmX8+PHq0qWLli5dqrp168rX19fp+OTJkz0xLQAAAAAAAAAAbuWREH3cuHH6+uuvVbNmTUnKd2NRAAAAAAAAAACKA4+E6JMnT9b777+vfv36eWJ4AAAAAAAAAAAKhUdqovv7++uGG27wxNAAAAAAAAAAABQaj4ToQ4cO1bRp0zwxNAAAAAAAAAAAhcYj5VzWrVunb7/9Vl999ZXq1KmT78aiCxcu9MS0AAAAAAAAAAC4lUdC9NKlS6t79+6eGBoAAAAAAAAAgELjkRB95syZnhgWAAAAAAAAAIBC5ZGa6AAAAAAAAAAAXA08shM9Li5OhmG4PL57925PTAsAAAAAAAAAgFt5JEQfNmyY0/OcnBz98ssvWrp0qZ544glPTAkAAAAAAAAAgNt5JEQfOnToBdtff/11bdiwocDjvPnmm3rzzTe1Z88eSVKdOnX0/PPP69Zbb5UkmaapsWPH6p133lFqaqqaNm2q119/XXXq1HGMkZWVpZEjR2r+/PnKzMxU27Zt9cYbb6hixYqXf4EAAAAAAAAAgBKhUGui33rrrfrss88K3L9ixYp6+eWXtWHDBm3YsEE333yzbr/9dm3btk2SNHHiRE2ePFnTp0/X+vXrFR0drfbt2ysjI8MxxrBhw7Ro0SItWLBAa9as0fHjx9WlSxfZbDa3Xx8AAAAAAAAA4OpSqCH6p59+qoiIiAL3v+2229SpUyfVqFFDNWrU0EsvvaRSpUrpxx9/lGmamjp1qp555hl1795d8fHxmj17tk6ePKl58+ZJktLS0jRjxgxNmjRJ7dq1U4MGDTRnzhxt2bJFy5cv99RlAgAAAAAAAACuEh4p59KgQQOnG4uapqnk5GQdOnRIb7zxxmWNabPZ9Mknn+jEiRNq3ry5EhMTlZycrA4dOjj6+Pv7q1WrVlq7dq0GDhyojRs3Kicnx6lPTEyM4uPjtXbtWnXs2NHlfFlZWcrKynI8T09Pv6x1AwAAAAAAAACKL4+E6N26dXN6brFYVLZsWbVu3VrXXnvtPxpry5Ytat68uU6dOqVSpUpp0aJFql27ttauXStJioqKcuofFRWlvXv3SpKSk5Pl5+en8PDwfH2Sk5MvOu/48eM1duzYf7RWAAAAAAAAAMDVxSMh+pgxY9w2Vs2aNbVp0yYdO3ZMn332mfr27avVq1c7jp+7413K2/V+ftv5CtJn9OjRGj58uON5enq6YmNjL+MKAAAAAAAAAADFVaHWRL8cfn5+qlatmho3bqzx48fruuuu0//93/8pOjpakvLtKE9JSXHsTo+OjlZ2drZSU1Nd9nHF399foaGhTg8AAAAAAAAAQMni1hDdYrHIarVe9OHjc2Wb303TVFZWluLi4hQdHa1ly5Y5jmVnZ2v16tVq0aKFJKlRo0by9fV16pOUlKStW7c6+gAAAAAAAAAA4Ipby7ksWrTI5bG1a9dq2rRpMk2zwOM9/fTTuvXWWxUbG6uMjAwtWLBAq1at0tKlS2UYhoYNG6Zx48apevXqql69usaNG6egoCD16tVLkhQWFqYBAwZoxIgRKlOmjCIiIjRy5EjVrVtX7dq1u+LrBQAAAAAAAABc3dwaot9+++352n777TeNHj1aX375pXr37q1///vfBR7v77//1r/+9S8lJSUpLCxM9erV09KlS9W+fXtJ0qhRo5SZmalBgwYpNTVVTZs21TfffKOQkBDHGFOmTJGPj4969OihzMxMtW3bVrNmzZLVar3yCwYAAAAAAAAAXNU8cmNRSTp48KDGjBmj2bNnq2PHjtq0aZPi4+P/0RgzZsy46HHDMJSQkKCEhASXfQICAjRt2jRNmzbtH80NAAAAAAAAAIDbbyyalpamJ598UtWqVdO2bdu0YsUKffnll/84QAcAAAAAAAAAwNvcuhN94sSJmjBhgqKjozV//vwLlncBAAAAAAAAAKC4cGuI/tRTTykwMFDVqlXT7NmzNXv27Av2W7hwoTunBQAAAAAAAADAI9waot93330yDMOdQwIAAAAAAAAA4DVuDdFnzZrlzuEAAAAAAAAAAPAqt99YFAAAAAAAAACAqwUhOgAAAAAAAAAALhCiAwAAAAAAAADgAiE6AAAAAAAAAAAuEKIDAAAAAAAAAOACIToAAAAAAAAAAC4QogMAAAAAAAAA4AIhOgAAAAAAAAAALhCiAwAAAAAAAADgAiE6AAAAAAAAAAAuEKIDAAAAAAAAAOACIToAAAAAAAAAAC4QogMAAAAAAAAA4AIhOgAAAAAAAAAALhCiAwAAAAAAAADgAiE6AAAAAAAAAAAuEKIDAAAAAAAAAOACIToAAAAAAAAAAC4QogMAAAAAAAAA4AIhOgAAAAAAAAAALhCiAwAAAAAAAADgAiE6AAAAAAAAAAAuEKIDAAAAAAAAAOACIToAAAAAAAAAAC4QogMAAAAAAAAA4AIhOgAAAAAAAAAALhCiAwAAAAAAAADgQpEO0cePH68mTZooJCRE5cqVU7du3bRz506nPqZpKiEhQTExMQoMDFTr1q21bds2pz5ZWVkaMmSIIiMjFRwcrK5du+rAgQOFeSkAAAAAAAAAgGKoSIfoq1ev1uDBg/Xjjz9q2bJlys3NVYcOHXTixAlHn4kTJ2ry5MmaPn261q9fr+joaLVv314ZGRmOPsOGDdOiRYu0YMECrVmzRsePH1eXLl1ks9m8cVkAAAAAAAAAgGLCx9sLuJilS5c6PZ85c6bKlSunjRs3qmXLljJNU1OnTtUzzzyj7t27S5Jmz56tqKgozZs3TwMHDlRaWppmzJihDz/8UO3atZMkzZkzR7GxsVq+fLk6duxY6NcFAAAAAAAAACgeivRO9POlpaVJkiIiIiRJiYmJSk5OVocOHRx9/P391apVK61du1aStHHjRuXk5Dj1iYmJUXx8vKPPhWRlZSk9Pd3pAQAAAAAAAAAoWYpNiG6apoYPH64bb7xR8fHxkqTk5GRJUlRUlFPfqKgox7Hk5GT5+fkpPDzcZZ8LGT9+vMLCwhyP2NhYd14OAAAAAAAAAKAYKDYh+qOPPqpff/1V8+fPz3fMMAyn56Zp5ms736X6jB49WmlpaY7H/7d331FVXIvbx59DEQ0W7MYSY+wt9hI7dixRo1Fj7xp7iUaNscbeQUXFgjUYK7FXsCeWWGI3dkUQK4hInfcPX+ZClHtv7k89At/PWq51z5yZYU/u7LNnP7Nnz507d/63ggMAAAAAAAAAEqwEEaL36dNHv/76q3x8fJQ9e3ZzeZYsWSTptRHlDx48MEenZ8mSReHh4Xry5Em867yJg4ODUqdOHecfAAAAAAAAACBp+aBDdMMw1Lt3b23YsEH79u1Trly54nyfK1cuZcmSRbt37zaXhYeHa//+/apQoYIkqVSpUrK3t4+zzv3793Xu3DlzHQAAAAAAAAAA3sTO2gX4d3r16qXVq1fL29tbqVKlMkecp0mTRilSpJDFYlH//v01YcIE5c2bV3nz5tWECRP00UcfqVWrVua6nTt31qBBg5Q+fXqlS5dO3333nYoWLaqaNWta8/AAAAAAAAAAAB+4DzpEd3d3lyRVq1YtzvKlS5eqQ4cOkqQhQ4YoNDRUPXv21JMnT1SuXDnt2rVLqVKlMtefOXOm7Ozs1Lx5c4WGhqpGjRry9PSUra3t+zoUAAAAAAAAAEAC9EGH6IZh/Md1LBaLRo8erdGjR8e7TvLkyeXm5iY3N7e3WDoAAAAAAAAAQGL3Qc+JDgAAAAAAAACANRGiAwAAAAAAAAAQD0J0AAAAAAAAAADiQYgOAAAAAAAAAEA8CNEBAAAAAAAAAIgHIToAAAAAAAAAAPEgRAcAAAAAAAAAIB6E6AAAAAAAAAAAxIMQHQAAAAAAAACAeBCiAwAAAAAAAAAQD0J0AAAAAAAAAADiQYgOAAAAAAAAAEA8CNEBAAAAAAAAAIgHIToAAAAAAAAAAPEgRAcAAAAAAAAAIB6E6AAAAAAAAAAAxIMQHQAAAAAAAACAeBCiAwAAAAAAAAAQD0J0AAAAAAAAAADiQYgOAAAAAAAAAEA8CNEBAAAAAAAAAIgHIToAAAAAAAAAAPEgRAcAAAAAAAAAIB6E6AAAAAAAAAAAxIMQHQAAAAAAAACAeBCiAwAAAAAAAAAQD0J0AAAAAAAAAADiQYgOAAAAAAAAAEA8CNEBAAAAAAAAAIgHIToAAAAAAAAAAPEgRAcAAAAAAAAAIB6E6AAAAAAAAAAAxOODD9EPHDighg0bKmvWrLJYLNq0aVOc7w3D0OjRo5U1a1alSJFC1apV0/nz5+OsExYWpj59+ihDhgxydHTUl19+qbt3777HowAAAAAAAAAAJEQffIgeEhKiYsWKac6cOW/8fsqUKZoxY4bmzJmj48ePK0uWLKpVq5aCg4PNdfr376+NGzfKy8tLhw4d0vPnz9WgQQNFRUW9r8MAAAAAAAAAACRAdtYuwH/i4uIiFxeXN35nGIZmzZqlH374QV999ZUkadmyZcqcObNWr16t7t2769mzZ1q8eLFWrFihmjVrSpJWrlypHDlyaM+ePapTp857OxYAAAAAAAAAQMLywY9E/3du3Lghf39/1a5d21zm4OCgqlWr6siRI5KkkydPKiIiIs46WbNmVZEiRcx13iQsLExBQUFx/gEAAAAAAAAAkpYEHaL7+/tLkjJnzhxneebMmc3v/P39lSxZMqVNmzbedd5k4sSJSpMmjfkvR44cb7n0AAAAAAAAAIAPXYIO0WNYLJY4nw3DeG3Z3/2ndYYNG6Znz56Z/+7cufNWygoAAAAAAAAASDgSdIieJUsWSXptRPmDBw/M0elZsmRReHi4njx5Eu86b+Lg4KDUqVPH+QcAAAAAAAAASFoSdIieK1cuZcmSRbt37zaXhYeHa//+/apQoYIkqVSpUrK3t4+zzv3793Xu3DlzHQAAAAAAAAAA3sTO2gX4T54/f66//vrL/Hzjxg2dPn1a6dKl0yeffKL+/ftrwoQJyps3r/LmzasJEyboo48+UqtWrSRJadKkUefOnTVo0CClT59e6dKl03fffaeiRYuqZs2a1josAAAAAAAAAEAC8MGH6CdOnJCzs7P5eeDAgZKk9u3by9PTU0OGDFFoaKh69uypJ0+eqFy5ctq1a5dSpUplbjNz5kzZ2dmpefPmCg0NVY0aNeTp6SlbW9v3fjwAAAAAAAAAgITjgw/Rq1WrJsMw4v3eYrFo9OjRGj16dLzrJE+eXG5ubnJzc3sHJQQAAAAAAAAAJFYJek50AAAAAAAAAADeJUJ0AAAAAAAAAADiQYgOAAAAAAAAAEA8CNEBAAAAAAAAAIgHIToAAAAAAAAAAPEgRAcAAAAAAAAAIB6E6AAAAAAAAAAAxIMQHQAAAAAAAACAeBCiAwAAAAAAAAAQD0J0AAAAAAAAAADiQYgOAAAAAAAAAEA8CNEBAAAAAAAAAIgHIToAAAAAAAAAAPEgRAcAAAAAAAAAIB6E6AAAAAAAAAAAxIMQHQAAAAAAAACAeBCiAwAAAAAAAAAQD0J0AAAAAAAAAADiQYgOAAAAAAAAAEA8CNEBAAAAAAAAAIgHIToAAAAAAAAAAPEgRAcAAAAAAAAAIB6E6AAAAAAAAAAAxIMQHQAAAAAAAACAeBCiAwAAAAAAAAAQD0J0AAAAAAAAAADiQYgOAAAAAAAAAEA8CNEBAAAAAAAAAIgHIToAAAAAAAAAAPEgRAcAAAAAAAAAIB6E6AAAAAAAAAAAxIMQHQAAAAAAAACAeCSpEH3evHnKlSuXkidPrlKlSungwYPWLhIAAAAAAAAA4AOWZEL0NWvWqH///vrhhx906tQpVa5cWS4uLrp9+7a1iwYAAAAAAAAA+EAlmRB9xowZ6ty5s7p06aKCBQtq1qxZypEjh9zd3a1dNAAAAAAAAADAB8rO2gV4H8LDw3Xy5EkNHTo0zvLatWvryJEjb9wmLCxMYWFh5udnz55JkoKCgt5dQd+DF8+DrV0ExBIU5GjtIuD/o258WKgbHw7qxoeFuvHhoG58OKgXHxbqxoeDuvFhoW58OKgbHw7qxYclMdSNmNzWMIy3vu8kEaI/fPhQUVFRypw5c5zlmTNnlr+//xu3mThxosaMGfPa8hw5cryTMgIAAAAAAAAA/m+Cg4OVJk2at7rPJBGix7BYLHE+G4bx2rIYw4YN08CBA83P0dHRevz4sdKnTx/vNng/goKClCNHDt25c0epU6e2dnGADwZ1A3gz6gbwZtQN4HXUC+DNqBvAm1E3PiyGYSg4OFhZs2Z96/tOEiF6hgwZZGtr+9qo8wcPHrw2Oj2Gg4ODHBwc4ixzcnJ6V0XE/yB16tT8QAFvQN0A3oy6AbwZdQN4HfUCeDPqBvBm1I0Px9segR4jSbxYNFmyZCpVqpR2794dZ/nu3btVoUIFK5UKAAAAAAAAAPChSxIj0SVp4MCBatu2rUqXLq0vvvhCCxcu1O3bt9WjRw9rFw0AAAAAAAAA8IFKMiF6ixYt9OjRI40dO1b3799XkSJFtG3bNuXMmdPaRcM/5ODgoFGjRr023Q6Q1FE3gDejbgBvRt0AXke9AN6MugG8GXUj6bAYhmFYuxAAAAAAAAAAAHyIksSc6AAAAAAAAAAA/C8I0QEAAAAAAAAAiAchOgAAAAAAAAAA8SBEBwAAAAAAAAAgHoToAAAAAAAAAADEgxAdACDDMKxdBCBBio6OlkQdQtLFuQ8AAPDPcQ2V8BCiAwBksVjifKZBB/47NjavLqXu3btn5ZIA1hHTfkycOFH79u2TRBsCAHiz2O0DbQWSuphrKH9/fyuXBP8tQnQkGTGjBQG82dy5c1WrVi1Jr4fqAOK3adMmNW/eXI8ePbJ2UQCrOX78uCZNmqSwsDDaECQJfw8A6WsA/1lM++Dq6qpjx45Jou4gaZs7d66GDRsmiRtLCQEhOpKMmNGCu3fvVmBgID9QwN9kzpxZQUFBunDhgrWLAiQooaGhunv3roKCgiTRGUTS1KJFCz169Ei3bt2SRD1A4hcTBi5fvly3b982+xoA/rOVK1dq3LhxkkTdQZKWKVMmrVq1SqdOnWIQQgLArxWSjOjoaJ0/f1516tTRlStXZLFYCNKRZL3p3C9btqwePHigjRs3WqFEQMIQOxiMqUfffPONcufOrYEDB0qiM4jELb5H8Vu0aKGwsDBNmTJFEvUAScO1a9c0depU+fr6SpKioqKsWyDgAxdzHTVs2DA9evRIZ8+elcQIXCQNf7+Gio6OVq1ateTi4qItW7ZIYhDCh46rWyQZNjY2Kly4sFq2bKlJkybp+fPn3OlDkhVz7oeHh5vLPvnkE/3www9asmSJLl26ZK2iAR+02MFg7Dake/fuCgwM1MWLFyXRGUTiFXPee3p6asGCBXr+/Ln53ciRI3X69GmdOXPGWsUD3qvcuXOrQIECWrJkiSTJ1tbWyiUCPix/vx6KuY6qWLGiAgMD9csvv0hiKkkkDTHneXBwsCwWi2xsbOTk5KTPP/9cixYt0suXL2VjY0M/4gNGiI5E6+938CIiIiRJDRs2lJ+fn/z8/N64HpBUTJgwQR06dDA7fpJUrVo1pUqVSn/++ackRlQBbzJv3jzlyZNHS5cu1fXr1yVJNWvWlJ+fnzw9PSXRGUTiFhERoRUrVmjx4sXKnz+/Fi1apLNnz6pBgwZ69uyZDh8+LImbSUhc/t5niLlGmjBhgu7evauff/7ZGsUCPmgx10Nr1qzRvHnzzOWZMmXSiBEj9Msvv5ij0YGkwNPTU7Vr19aWLVsUGBgoSRo3bpzSp0+v8ePHS6If8SEjREeiFXOX28fHR48ePZK9vb2kV4/dh4WFmT9QPG6MpKpAgQKKiorSTz/9pEqVKmnRokX69NNPVadOHY0aNUrh4eGMqAL+xjAMVatWTRUqVNDSpUtVoUIFTZkyRUFBQXJ1ddWuXbt07tw5axcTeKv+Hh7a29trx44d2rZtm9q3b69Vq1apXr16cnd3V+nSpTVt2jTdu3ePTiASlZg+w6+//qrnz5+bIXq6dOlUtGhRHThwQBI3j4C/e/jwoZYsWaKpU6eqZMmSmj9/vm7cuKF69erJycnJDNEZvIOkwMbGRsWKFVPbtm3VoUMHjRkzRkFBQapSpYquX79OPfjAWQxaeSRiPj4+6tWrl548eaKffvpJRYoUUbly5bR+/XpNnz5dc+fOVYkSJaxdTOCdi46OfuMNo+fPn+vJkycaNWqUrl69qps3b6p58+Zavny53N3d1axZMxmGQRCCJCu+uiO9mgt3165dWrJkiaKiohQdHa2goCBNmTJFzZo1+7fbAglF7PP45MmTkl51AGNfP924cUOnT5/W2LFjFRkZqfPnz2vt2rVq2rSpoqKiuCGLROPq1asqXry48ufPr1KlSmnQoEEqUKCADh48qNq1a2v37t2qVKmStYsJWNWbrn+ePXumqKgoDRs2TDdu3NAff/yhmTNnau7cuXr58qWOHj2qFClSWKnEwLvx7/oCv/32mw4cOKDZs2erRIkSslgs2rp1q9asWaOvv/76PZcU/y1CdCQqfw/7IiMjdfPmTS1fvly7d+/WgwcP9PXXX6tYsWIaMWKERo0apXbt2lmxxMC7F7vx3rRpk+7cuSOLxaKWLVsqQ4YM5noPHjzQypUrtX79eh07dkxNmjQx5ykEkqLYdSfmceP06dOrePHicnZ2Nte7deuWrl27psmTJ+vgwYPKmTOnjh49KicnJyuVHHg7Yl9X/fjjj/r5559lsVj04MED/fTTT+rWrZscHBzM9R8/fqwbN25o+PDhevDggU6dOmWtogNvxZsGErx48ULu7u46cOCA9u7dq44dO6pMmTI6cOCA0qdPr4kTJ0riaVckTbGvnc6dO6eIiAhlzpxZWbNmNdcJCAiQl5eXvL29df/+fV2+fFleXl5q3rw5g3eQaMSuC+vXr5e/v7/Cw8PVrl07pU2b1vwuJCREbm5uunLlijw9PdWoUSN5enoqderU1IUPECE6Eo3YP1KhoaEKCQmJExBevXpVly5d0vDhw1W4cGH98ssvypUrl/bt26ecOXNaq9jAOxX7QnTo0KFas2aNMmXKJAcHB927d0979uxRrly54mwTEBCgI0eOqGvXrlqxYoVcXFysUXTAqmLXnSFDhmjlypUqU6aMnj9/rqCgIPXt21dt27Z9bbvNmzdr8uTJGjp0qBo0aEBnEAlW7OuqcePGae7cuVqzZo1KlSqloUOHat68eRo9erSGDx8uOzu7OOf61atX9eWXX2rBggWqUqWKNQ8D+J/FrgMBAQGyt7eXnZ2dUqdObZ7vK1eu1JEjR7Ru3To9fPhQn3zyic6ePRtnHSCpiH3Ojxw5UqtWrZIkBQYGau7cuapfv77SpUtnrn/nzh3dv39f3bp1U9asWbVt2zarlBt4l4YOHSpPT08VK1ZMZ86cUeHChTV48GDVrl37tZutixcv1pAhQ+Tr66uiRYtaqcT4d7g9jkQh9kXupEmT9OWXX6pUqVIaPHiwzp8/L0nKmzevGjZsKB8fH3Xv3l3dunXT06dPdfz4cUnMwYbEKeZC1tXVVStWrNAvv/yi33//Xe3bt9eNGzdUqVIlXbx4UdKrJzckKXPmzHJ2dtbnn3+ua9euWa3sgDXF1J158+Zp7dq12rBhg7y9vfX111/r7NmzGjVqlBYsWGCuHx4eLkmqX7++JGn79u1x9gMkFN7e3goJCTGvqy5duqTff/9dS5YsUdWqVbV3716tXr1abdq00dixYzVx4kSFhYXFOdczZsyo0NBQPXv2zFqHAfyfxO5b/PTTT2revLmKFSumQYMGac+ePeb53qZNG82aNUu//fabBg0aJFtbW02YMEESv/9IWqKiosxzfuzYsfLw8JC7u7uuXr2qBg0aqG/fvvL09FRQUJC5Tfbs2VW2bL3TeFMAADrCSURBVFmtWbNGf/zxhw4dOmSt4gNvTez3yLi6umrVqlXatm2bdu7cqTlz5sjHx0fjx4/Xzp07zXVjsqjOnTvr888/19KlS61SdvxnhOhI0GIepIi5yB0xYoRmz56t+vXra/bs2Vq0aJF++uknHT582NwmQ4YMcnZ21vz589WwYUNNnTpVkpivE4nG9OnTdfToUfPzgwcPdOHCBU2bNk1lypTRli1bNGDAAE2YMEEFChRQnTp1dPXqVdnZ2ZkNuZOTk8LDw80XJPLQEpKCXr166cyZM+bnly9f6sKFC/r2229Vvnx5/frrrxo6dKiGDRumChUqaOzYsVqxYoUkKVmyZGbokitXLgUFBSkiIsJahwL8TxYsWKD+/ftr/vz5Cg0NlSSlTZtWDRo0kLOzsw4ePKjevXtr3LhxWr58uVq3bq1Ro0ZpxIgRcQYj7NmzR7dv31bBggWtdSjA/0lM3+LHH3/UrFmz1KtXL02bNk3Xr19X3759tXXrVnNdi8Wizz77TD/99JPatGmj06dPmwMTgMTO29tb0r/60hcuXNDBgwfl4eGh2rVra/PmzdqxY4cqV66s7777TosWLdKTJ08kvao7hmEoXbp0Sps2LddNSNCGDBmiY8eOycbGRoZh6OnTp7p7965GjhypkiVLav369erataumTJmioKAgDR06VNu3bzffHRPTD4+Ojpa9vb2VjwbxMoAE6unTp4ZhGEZkZKRhGIaxbds2I3/+/MahQ4cMwzCMY8eOGXZ2dkbGjBmNWrVqGb/99pu5bVhYmGEYhrFnzx6jZMmSxv37999z6YF349KlS4ajo6PRsmVL48SJE+byvXv3Gjdv3jTOnj1rfPbZZ8bcuXMNwzCMxYsXGxaLxbCxsTFu3Lhhru/r62t8+umnxpkzZ973IQBWERISYhQoUMDImzevcf78eXN5QECA8ddffxl//fWXkTdvXmPmzJmGYRiGt7e3kTx5csPR0dFYu3atuf7Ro0eN7NmzU3eQIIWGhho9evQwypYta0yZMsV4/vy5YRiGERQUZBiGYfTr189o166dERoaahiGYQwePNioVq2aUaVKFSM6Otrcz8GDB40rV668/wMA/g8eP34c5/OOHTuMwoULm32IPXv2GMmTJzfKly9vFChQwNixY4e5bkREhGEYr/ofGTNmNC5evPj+Cg5YycKFC43PPvvMmDp1qrns1q1bxpIlS4zw8HDjwIEDRtasWQ03NzfDMAyjWbNmRrp06YyxY8ea7YthGMby5csNi8ViXL9+/b0fA/A2nD171qhQoYJRpkwZ49SpU4ZhGEZ4eLhx6NAh48GDB8b58+eNfPnyGbNmzTIM41X7Ym9vbxQrVszMrwzDME6fPm1YLBbj9OnT1jgM/BcYiY4EacSIEapevboCAgJka2uryMhIpU6dWn379lXFihW1Y8cO1alTR8uWLdOBAwe0f/9+zZgxQ3v37pX0asSgJG3YsEF+fn5Knjy5NQ8HeCsMw1D+/Pm1e/dunThxQtOmTdNvv/0mSapevbpy5sypU6dOKXfu3GrTpo2kV09mdOnSRT/++KOyZ89u7qtAgQI6cuSIPv/8c6scC/C+ffTRR/r999/18ccfq2HDhuZUYJkyZVLu3Ll1/PhxOTk5qUOHDpKkFClSmE89NWnSxNxP+fLldeLECeoOEpywsDAlT55c7u7uKlasmDZv3myOSE+VKpVevnypM2fOyNbWVsmTJ1dERISuXLmiIUOGaP/+/bJYLOYoqkqVKilv3rxWPiLgv9erVy998cUXun//vrksV65cqlevnsqVK6ft27erZcuWcnNzk5ubm0JDQ9WnTx+tW7dOkmRnZydJ2r17t5IlSxZn3mcgsapdu7ZcXFy0fv16TZo0SZL0ySef6Msvv5S9vb1WrlypunXrqnv37jIMQ5kyZVL27Nm1c+dOffTRR5Je9V8KFCigCxcuvPaeJiChKFq0qMaNG6csWbKoS5cu+uOPP2Rvb6+yZcsqY8aMOnHihDJkyKCWLVtKkoKCgtSiRQt98cUXKl++vLmfYsWKKSAgQMWKFbPWoeA/IERHgpQ3b16lSpVKHTp0kL+/v+zs7FSoUCE1adJEwcHBmjp1qr777ju1atVKuXPnVr58+bRu3TozRJdeNdhOTk7y9vaWk5OT9Q4GeEtiHqX/4osvNGfOHB05ckTu7u46efKkuY6fn585vVFwcLCWLFmiVKlSafTo0bKzs4szL/rHH3/8/g8CsIKYupM6dWqtWLFCDg4O6tKlixmkS5K9vb1u376t/fv3KyQkRK6urvrkk0/UqVMn2draKioqygwQM2fObJXjAP5XhmHIwcFBkuTl5aU0adLo3LlzmjZtmhYsWKAXL14oefLkat68uTw9PfXVV1+pTJkyun79umrVqmXu4+8vyAISiv79+ys6OlotW7aUn5+fJClfvnwaMWKEIiMjNW/ePPXq1UtdunRR6dKllT9/ftna2urXX3+V9K9p74KCgrRlyxZlypTJascCvA8RERHKmTOnxo4dq/Lly2vr1q2aNWuWJCl9+vR6+fKl/vrrL6VMmVL29vayWCy6f/++VqxYoYMHD5pTuVgsFpUpU0YFChSw7gEB/6OYaYiqV6+udu3aKWPGjPr22291/vx52dvbyzAMBQQE6OnTp7p//76ePHmiFStWqFixYnJ3dzf7ETEyZsxorUPBf8FiGEx0i4Rp/fr1Wrx4sSIjI7Vq1SrzxyYwMFC1atXS4MGD1bp1awUHB2vIkCFq0aKFKleuzNznSJRiLkIl6YcfflBISIjWr1+ve/fuqWHDhho5cqRKlSolf39/1a9fX+fPn1euXLlkY2OjM2fOmCOogKRs6NChunjxou7du6dTp04pf/78WrdunQoVKqSrV69qxIgR2r17t5ycnJQyZUqdPHnSvDjmBXJIDEaNGiU3NzdNnz5d9vb2Wrp0qR48eKD27durZ8+e+uijj7Ro0SL5+PgoU6ZMmjJliuzt7c35PIGEKDIyUnZ2drp165bq1KmjbNmyadmyZeYTeo8ePVLp0qU1aNAg9e7dW48fP1bPnj3VrFkzNW3a1HwKg5tISCpiX/esW7dOe/bs0YYNG2RnZ6ehQ4eqb9++kl49PT5lyhS1aNFCFy5cMJ9osrOz49oJiULs83j8+PE6ceKEbt68qTNnzqh06dJauHChihcvrtu3b6tixYpmO+Hk5KQTJ04w93kCRIiOBCX2Ber27du1c+dOubu7q27dulq0aJEyZsyoW7duqXr16qpYsaKqVq2qtWvX6unTpzp69KgsFgsdPSRqs2fP1ujRo7V161Y5Ojrq7t276tChg6pUqaIff/xRxYsXV0BAgDZu3Ch7e3u1b99ednZ21AskefPnz9f333+vXbt2KWPGjHr48KH69u2rhw8fytvbW4ULF9bVq1d148YNPXjwQN988405nRg3oZDQxYySqlmzpgYMGKDOnTtLksLDw9W1a1cdOHBAAwYMUJcuXfTRRx8pPDzcnBqPOoCELHbfYvfu3bp48aL69++vevXqacGCBcqWLZtCQkLUtWtX3b17V02aNNG2bdv04sULHTx4UDY2NgToSLJ++OEHLVy4UGPHjpWtra2WLVum0NBQtWjRQt9//70kacyYMbp48aKcnJzk5ubGjVckSnPmzNHQoUPl7e2tPHnyaO/evVq1apWCgoI0f/58lSpVSnfu3NHu3btla2ur1q1bm0+Bcw2VsBCiI0EaMGCAdu3apRo1aujChQu6ePGiihYtKk9PT2XJkkW7d+9Wz549lSpVKqVNm1Y7duxgtCCShFatWsnOzk7Lly83lx06dEguLi6qUaOG+Xbw2LiQBaTBgwfr+vXrWr9+vbnM399f9erVU2RkpLy8vFSoUKE421B3kJD9/ZooODhYFStWVOfOndWvX784HbuiRYsqIiJCzZs31/Dhw3mXDBKd77//XqtXr1bXrl11+fJl7d27V/ny5dOaNWv08ccfa8eOHVq6dKk5b/P69etlb29PgI4kJXa74efnp5o1a+rHH3/UN998I0m6ffu2xo4dq0OHDqlXr17q06ePpFfv3IiZMozQEIlNVFSUOnTooBQpUmjhwoXm8q1bt2rEiBFycHDQwoULX3tfEv2IhIkWHwnO4cOH5eXlpfnz58vV1VV79uzRuHHjFBQUpA4dOiggIEC1atXSgQMHtGvXLu3Zs0f29vaKjIwkQEeiEjP/cozIyEhFRkYqLCzM/BwREaFKlSqZ01CMGjUqzjzPkmi8keT8ve5I0rNnz3Tp0iXzc2RkpLJkyaLu3bvr3Llzql69um7cuBFnG+oOEqro6Gjzmujhw4eSXr10PX369Nq9e7ckmU8pSVKRIkUUHh6up0+fmkEIkFCFh4fH+XzmzBktWbJEHh4eGjlypFatWqWdO3fq7t27at68uQICAlS3bl0tX75cBw8elLe3t9m3IEBHUhG73bh3755SpkypiIgIBQYGSnoVsH/yyScaP368wsPDNWvWLI0cOVKS4rQbBOhIbGxtbZUqVSpduXLF7IdLUv369eXi4qJjx47pyy+/jNPPiNkOCQ+tPhKc4OBghYaGKmvWrOaytm3bqnnz5jpw4IC6d+8uPz8/ffzxx8qQIYM5TyENNhKT2COffv/9d718+VJ2dnZq2LCh1q5dq71798rOzs4871OkSKEKFSrI0dFRBQsWtGbRAauKXXeOHDmiK1euSJK6dOmily9faty4cZL+1cmLCdKbN2+uTz75xDqFBt6i2HVg4sSJateunS5duiQHBwfNmTNHBw8eVLdu3fTy5UtJ/xp5OHPmTM2aNct8GRyQEDk7O5s3imLEnOv58uWT9OqcL1asmFasWKGTJ0+qZ8+eun37thwcHOTk5ETfAklSTLsxaNAgfffdd7p//76yZ8+u06dPKyQkxFwvc+bMKleunFKmTKmgoCDaCyQqbxqII0mff/657t+/r507d5ptiiQVKFBALi4u6tatm/Lmzfu+iol3iBAdH7TYjW7M/86ePbuyZ8+uEydOmD9i9vb26tixo3LkyKHDhw9r8uTJcfbDKBEkJrEDkB9//FF9+vSRl5eXoqKi9PXXX6tLly5q1KiRtmzZoidPnigoKEh79uxR69at5eXlZc7fCSQ1sevOsGHD1Lt3bx0/flwvXrxQnjx51Lx5c23ZskXff/+9nj9/rhs3bsjDw0MpU6aUq6urbG1tzZG5QEIVUweGDBkiNzc3tWrVynyxVeHChbV69WqtWbNGFStWVMOGDVW+fHn98ccfatCggdl+8GQfEqpatWqpZs2akmT+nhcsWFCGYWjt2rWSZJ7fuXPnVs6cObVx40ZNmDAhzn7oWyApunTpkvbs2aM+ffoof/78+uGHH7Rs2TJNmDBBjx8/lvRq6paoqCgNGjRIM2fO5MYrEo3Y/YhNmzZpzZo12rx5sySpR48eypcvnwYNGqR169bp1q1bevLkiTZs2KBSpUpp2LBh9CMSCeZExwcr9o9URESEoqKilDx5coWGhqpRo0YKDg7WrFmzVK5cOUnSnTt31L9/f7Vo0ULNmjXj4haJ3vDhw7Vw4UKtW7dOhQsXVsaMGSVJjx490pgxY+Tu7q5PP/1UkZGRSpEihc6cOcO7AQBJo0ePlru7u1avXq3y5cvL0dFR0qu6s2DBArm7u+vJkyfKkCGDnJycdPz4cTNkBBKDrVu36ttvv9XatWvN66jg4GDdvHlTRYsWlb+/v2bOnKmgoCClSJFCU6ZMkZ2dHfM/I8H6+7k7YcIEZcqUSS1btlTKlCk1YsQI7dixQz179lSnTp0kvaoTffr00YABA1SkSBEevUeSNnHiRF2+fFmS5OHhYV4X/fLLL2rbtq2qVKmilClTyt/fX8HBwTpz5oxsbW1pN5AoxO4/Dx48WAsXLlTWrFl17do1ffvtt5o9e7YkqUWLFrpw4YLu3LmjrFmzyjAM/fnnn7Kzs6MPnkgQouODN3HiRO3fv19RUVHq2rWrmjdvrmfPnqly5cpKnjy5atasqaJFi8rDw0MODg7aunWrOVKKBhuJ1dmzZ9WqVSvNnz9flSpV0tOnT3X//n3t2rVLderUUYECBXTgwAHdvn1bUVFRvAEc+P/++usvNWnSRJMnT1a9evUUGBioW7duaefOnSpevLjq16+vkJAQbd++XWnSpFH16tVla2tL3UGC9vdrojVr1mj8+PE6e/aszp49q19//VXLli3TzZs31bp1a3l6er7W2aMOICGLXQeio6M1YMAAzZkzR8uXL1fr1q11/fp1TZw4Ub6+vqpSpYqKFSumDRs26Pnz5zp27JhsbGx4CRySlDfdeBoxYoQKFy4sX19fpU+f3mwnjh07po0bN+r27dvKkCGDpk2bxot3kSj5+fmpSZMm8vDwUNq0aXXs2DG1a9dOLVu21OLFiyVJx44d0/Xr1yVJX3/9tTkCnfYjcSBExwcndmM7adIkzZgxQ+3atdPt27e1bt06TZw4Ud9//72CgoL0/fff69SpUwoODlaOHDm0efNmGmwkCZcuXVKVKlX0888/K3PmzJo3b5727dunly9fKjAwUL///ruKFCkSZxsab0Dy9/dXvXr11LlzZxUtWlRLlizRqVOnZGNjowsXLmj27Nnq0aNHnG2oO0jIYofhvXv3VrJkydS2bVtVrVpVpUuX1tWrV1WjRg1VqVJF2bJlk4uLi44cOaLy5ctbueTA2xG7DgwaNEiPHj2Sp6en+vTpIw8PD3l4eKht27a6c+eOdu7cqdmzZ8vJyUlp06bV+vXr6VsgSbtz546yZcsmGxsbzZ8/Xz179tTEiRM1ZMgQc6qWN42u5cYrEpuJEyfq5MmTSpMmjdzd3ZUsWTJJr57ua9Gihb755ht5eHi8th39iMSFXzV8cGIuUK9cuSIHBwf9/PPPqlGjhiIjI1W5cmX1799fhmFo6NChmjNnjiIiIvT06VNlzpxZFouFBhuJzps6btmzZ5ezs7Pat2+vJ0+eqEOHDvrpp5/UrFkzFS5cWFu2bHktRKfxRlLzprqTMmVKFSxYUEuWLNHp06fVp08fTZ48WZUqVVLLli0VGBj42n6oO0ioYocbhw8flq+vr9zd3VWwYEGtW7dOXl5e6tq1q5ydnZUlSxY9fPhQ5cqVY/oiJBp+fn7KmjWrJMnX11c+Pj5ydXWVJLm5uSk6Olpdu3aVJLVu3VpdunRRp06dFB4eruTJk0siDETStXjxYk2ePFkLFy5UlSpV1KNHD4WEhGjIkCFKkSKF+vbta7Yxsa+5DMOgziDB+/s5nSJFCm3btk2FChUyA3RJql+/vtasWaNWrVopKChIa9asibMf+hGJC79s+CD5+PioRo0aypAhg37++WdJkp2dnfr06SOLxaL+/fvL1tZWgwcPlq2trbJkySLp1Q8dDTYSk9iN965du+Tn5ycHBwe5uLjI09NT+/btU5o0afTFF1/I1tZWL1++VKpUqcwOI5BUxa473t7eun79upInT64aNWpoxYoV+vPPPxUREaHSpUub2zx+/NgMTYDEICbcWL9+vTZt2qTatWurcuXKkqTatWurdu3akl6FhEFBQerQoYNsbW1VokQJq5UZeFtcXV01ffp03bhxQ5s2bdKWLVtUoUIFVapUSS9fvlTy5Mk1d+5cSVL37t1lY2OjRo0aKWXKlGZbQBiIpKxp06aaNWuWhg8frkmTJqlSpUoaNGiQJGngwIGyWCzq06ePpLgv22XeZyQGMee0v7+/smTJom+//VapU6dW9+7dNXr0aI0ePdpct379+lqyZInc3d15cimxM4AP0OPHj40xY8YY9vb2xty5cw3DMIzo6Gjz+7lz5xoWi8VYtWqVtYoIvFeDBw828uTJY5QuXdqoWbOmkTZtWuP8+fPm96Ghocbly5eNBg0aGCVLljQiIiKsWFrgwzF48GAjW7ZsRuPGjY2qVasaefLkMRYvXmx+HxISYly5csWoU6eOUbx4ceoOEoWYa6aoqCjjzp07houLi5E2bVqjZcuW5jqRkZGGYRjGy5cvjeXLlxuVK1c2SpcubYSHh5vbAgnV/PnzDQcHB+Pnn382DMMwatasaaRIkcKoVq2auU5YWJj5v3v37m1YLBZjx44d772swIcgvt/8Z8+eGcWLFzfKli1r7N+/31xv+vTphsViMX755Zf3WUzgvVq+fLmRJk0a49ixY4ZhvLp2mjdvnmFra2uMHTs23u24hkq8uD0Cq4uOjn5tWdq0aTVo0CANGDBAffr00Zo1a+Lc0e7Zs6fWrVun5s2bv8+iAlaxdOlSLVu2TKtXr9bx48fVokULPX36VOfPn5f0ap61NWvWqF+/fnr69Kl+++032dnZKSoqysolB6xr7dq1+vnnn7Vu3Tpt3LhR33zzje7cuaOUKVOa66xcuVK9e/dWeHi4jh07Rt1BohD7mil79uwaPXq0atasqX379mnlypWS/vV4cWhoqBwdHVWrVi0dPXpU9vb2ioyMZBQVEiwPDw/17dtXXl5eatmypSRpw4YNqlu3rgICAjR37lwZhqFkyZIpPDxc0qupXaZOnaoaNWpYs+iA1cT85q9atcrsY0hS6tSp5evrq5cvX6pfv346fPiwoqOjNXDgQK1atUpNmjSxVpGBd65ly5YqVKiQWrdurRMnTsjW1lbdunWTm5ubxo4dq/Hjx79xO66hEi9eLAqriv2oi5eXl+7cuaPnz5+radOmKlCggJIlS6bBgwdrxowZWr16tVq0aPHay0uYpxCJ3bBhw2RnZ6dx48Zp48aNateunWbMmKGuXbvq+fPnsrW1lZ+fn06dOqUmTZrI1taWegFIGj9+vC5cuKBVq1Zp/fr16tixo6ZOnaru3bsrJCREgYGBypAhg3x8fFSvXj3qDhKVJUuWyMPDQ/v371eyZMl04sQJTZkyRf7+/urdu3ecgQixz3tegIWEzNfXV9WrV9fo0aM1cuRIc3n//v0VFRUlPz8/PXjwQB07dlSnTp0kSWFhYXJwcDDXpR1AUmEYhqKjo+PcVHVyctIXX3yhBQsWKH/+/Oa6z549U758+VSiRAkNGDBAtWvXNvvk1BkkBn/PmWI+R0ZGqnr16rp7965++eUXlS5dWlFRUfLw8FDPnj21ZMkSdejQwXoFx3vF7RFYjWEYZoD+3XffqXfv3vL19ZWHh4datWql8ePH68WLF5oyZYq+++47tW3bVkuXLn1tjjUabCQmMU9mxL6/GRAQoIiICG3evFnt2rXT1KlT1bVrVxmGodWrV8vNzU25cuVSs2bNZGtrq6ioKOoFkrSYehQaGqpPP/1Uu3fvVocOHTRlyhR1795dhmFoy5Yt8vLykoODgxo2bChbW1veq4FEIzo6WsmSJVNISIi++uorhYeHq3Tp0ho4cKA+/vhjzZ07V+vWrTPXj33eE6AjIcuWLZsqVaqkkydP6sSJE5Jezeu8bds2TZ48WQsWLFDmzJm1bNkyLV26VJLiBOgSfQskHdeuXTN/8xctWqSHDx/q2rVrunr1qnr16qXLly+b6zo6Oqpw4cLatWuXNmzYEKdPTp1BQhcWFmae00uXLtWtW7dksVjM92Ls27dP2bJlU8uWLc0R6V26dNH69evVpk0bK5ce7xMhOqwm5kdq8+bN8vLy0q5du7R161b5+fmpfv362rt3r+bNmyfDMDRixAh16dJFS5YssXKpgXcr5sbSuXPnzGWFCxeWt7e32rRpo0mTJqlHjx6SpKdPn8rb21shISFxHhkjAEFS8/dpwWLqQ4ECBTRx4kS5uLhozpw5Zt0JCQnRkiVLFBgYKHt7+9e2AxKaN9WB5s2ba9SoUfLz89OXX36p8PBwlS9fXgMHDlTWrFk1cuRI7du3z0olBt6NvHnzavHixQoPD9fo0aNVuXJl3bx5U7t27dJHH32kDBkyaM6cOcqcObOmTJmirVu3WrvIgFWcPXtWBQsW1MqVKzV06FANHjxYoaGhyp49u37//XedO3dOPXv21IULFyS96l8UKlRIZ8+e1bx586xceuDt2bVrl9zc3HTixAkFBwdr6NCh+uqrr3T37t04QfrmzZsVFRWlfv366ciRI7Kzs1OTJk1kZ2enyMhIax8G3hN6i3jvdu3aJS8vL/Ozn5+f0qRJo9y5c5ujb0ePHq3ChQtr9erVMgxDqVKl0rRp03TgwAFrFRt4b/bv369ixYqZN42+/fZbpUuXTsmSJdNnn30mf39/Xb16Va1atdKDBw/0448/WrnEgPXEfqpp/fr1WrJkiX799VcZhqE2bdpo0KBBslgscnR01IULF3Tu3Dk1bdpUgYGBmjx5spVLD7wdMXVg27Zt5rJkyZKpYcOGGj58uB48eKDGjRsrPDxc5cqVU8+ePdWsWTNVrVrVWkUG3pm8efPK1dVVYWFh+vPPPzV06FB9+umnkqSIiAhlyZJFs2bN0ldffaW6detat7CAlWTKlEmjRo1St27dNH/+fF24cEH58uXTy5cvlT17dp04cUJXr15Vp06d1K5dO9WoUUMHDhxQoUKFzCdfgYRu6dKl6tSpk65fvy5JSpUqlU6cOKGwsDA1bdpUd+7cMQd/JkuWTPnz59fRo0c1c+bMOPvhaYykgznR8V4dPnxYlStXVqlSpdSnTx+1a9dOK1eu1MiRI/X7778rY8aMioiIkL29vW7fvq1cuXJpz549cnZ2Nvfx97mqgITu2bNnSpMmjfn50aNHmjZtmqZNmyZ3d3d16dJFISEhqlmzpoKDg3Xjxg19/vnnsrW1lY+Pj+zt7ZnDFklS7PZg0KBBWrFihRwdHZUiRQoVL15cq1atUnR0tAYMGCBPT0+lSJFCOXLkUOrUqbVz507qDhKVEydOqGnTpqpevbo5TYUkvXz5UqtWrdKgQYNUp04dLV++PM70FdQBJFbXrl1Tr169ZGNjo+HDh6tSpUqSZPY1YlAHkFQtWrRI3bp1U4oUKeTu7q527dpJ+td7AgICAjRixAg9evRIjo6OWrJkiezt7eO81wxIqLy8vNS5c2ctXbpUdevWVerUqc3v7t69q3r16ilZsmTauHGjsmXLJhsbG3Xt2lVDhw5Vrly5qANJFCE63qtNmzbpq6++UuXKlZU2bVq1bt1aLi4uypMnj+rWrStPT09z3XPnzqlFixb6+eef9fnnn1uv0MA7tHjxYh09elTz58+Pcwf78ePHmj59uiZOnKiFCxeqS5cuCgsL0+nTp3X79m19+umnKlWqlGxsbHiZD5KcmEuXmAD9xo0b6tmzp6ZOnaoMGTJox44dmjlzpnLlyqUNGzbIxsZGx48fV3h4uFKmTKmiRYtSd5Dg/X1QwbNnz7R06VKtWLFCJUqU0KJFi8zvAgICVKVKFd28eVM9evTQ7NmzGZSAJOHq1avq27evJGnEiBGqWLGilUsEWE/M737MjaPbt28rICBAO3fu1JQpU8yXrxuGEedJv9i4dkJi8ODBA3399ddq3ry5evXqZS5//vy5zp8/LwcHB6VLl06tW7fWrVu3VLNmTV25ckXPnz/XH3/8IRsbG27AJlGE6Hjv2rZtqzt37ih9+vQKDAzUkCFD5OTkpCZNmqhq1arq0qWLnJycNG7cOD1+/FiHDx/mLh8SpQULFujbb7/V/v37df78eX388cdq1KiR+X3sIH358uVvfGkJI0GQ1Ny6dUs5c+Y0z31PT08tWrRIWbJk0apVq+Tg4KCwsDBt3LhREydO1CeffCJvb+/X6gl1BwlZ7PM3LCxMkZGRcnR0VFBQkJYvXy4PDw+VKVPGDNIDAgI0ePBgtWzZUnXr1uXcR5Jy9epVDRgwQAEBAVq8eDGDc5AkxW43QkJClDx5cjMAvHXrljw8POTq6qoZM2aoS5cukqQJEyaoVq1aKlOmjCSeCEfi8eDBA1WrVk0TJkxQ48aNJUnu7u7at2+f1q9fryxZsqh48eLy9vbWoEGDFBAQIAcHBy1evJinMZI4/l/HexMWFiZJqlu3rvLly6chQ4YoY8aMmjZtmm7duqU9e/bor7/+UteuXdWuXTu9ePFCBw4ckI2NzWsvzAISuhUrVqh3797avHmzvvjiC/n4+Kh169bauXOnuU66dOnUt29fVapUSe3atYszqjAGjTeSksmTJytXrlw6e/asbGxsFBISonv37ikwMFCXLl0yp6hwcHBQkyZNNHz4cN27d0+VK1fW38cMUHeQEB06dEjSv87f8ePHq3Hjxqpevbo2btyo1KlTq2PHjurWrZt+++03Va9eXatWrdI333yjJ0+emAE6c9kiKcmbN6+mTp2qKlWqqEiRItYuDvDexQ78Zs2apaZNm6pu3brq2bOnJClnzpzq3r27+vXrp969e6tv376qVauWli1bppIlS5r7IUBHYhIUFKStW7dq3759atasmebNm6cMGTJo586dcnV11blz5+Tu7i5XV1etWbNGy5cvl729vSIjI+lHJGH8P493ysfHR4sXL5YkM9yoXr26tm3bpvPnz2vu3LlKnz69PDw8dP/+ff3xxx86cuSINm7cqL179/IjhUTJ09NT7du3l7Ozs+rXry87OzuNGTNGbdq0UcuWLbV9+3Zz3cyZM6tw4cIqWrSoPD09XwsCgaSkZs2aaty4serXr6/Tp0/L0dFRPXr0UK9evfTw4UN169bNXNfBwUGNGzdWnz59lCdPHuoOErzly5erSpUqWr16tSRp6tSpcnV1VYkSJZQrVy41a9ZMkydPlqOjozp16qSJEycqNDRUU6dOVfLkyc2pjaKjo3n8GElOwYIFNX36dAbnIEmK6UsPGzZMkydPlrOzs7766iutXbtWjRo1UmRkpHLkyKHevXtr5syZOnnypDJlyqRz587J1taWOoNEJ1OmTFq2bJnWrl2rrl276q+//tLs2bM1btw41apVSzVq1DBnTojNMAymM0rimM4F74yPj49q1KghSapdu7YaN26sSpUqqUiRIvLy8tLq1au1evVq3bp1S6NGjdKjR4/Upk0bde7c2dwHj8kgsfHw8FCPHj3UqVMnbdu2TV999ZXc3NwkSZcvX9b06dO1du1a/fLLL6pVq5ZCQ0PVqVMntW7dWvXr15fFYuFRSiRpFy9e1IQJE7R3717t2bNHhQoV0uPHj7Vs2TJ5enqqQoUKcnd3N9eP/QI52hQkZC9evNDs2bM1ZswYrVq1SmfPnlXlypVVs2ZNSZKbm5v69eunCRMmaMiQIea57ufnp48//lgWi4W5bAEgCYl5QagkeXt7a/jw4fLw8FCFChX066+/qlWrVjIMQyVLltS+ffvM66Xw8HDZ29vTbiDRCwwM1PPnz5UrV644y588eaJGjRqpTZs2cQbpAPwa4p3JkSOHKleuLDs7O4WHh+vChQsaMWKERowYIcMwFBISotOnT6tSpUoaO3asevXqpdOnT8cJCAk7kJjMmjVLAwcO1NatW+Xi4qIFCxZoxIgRkl6FH/nz59egQYNka2urOnXqqE6dOrp3755sbW3l4uJCgI4kK3b4ffr0aX366afy9/dXvXr19Ouvv+rzzz9Xu3btJL160qN3796aM2eOJJkdQok2BQnXkiVLdODAAS1YsECPHz9WixYtlC5dOn3xxRfmOn369JHFYlG/fv1ka2urnj17ytHRUVmzZpX0qh4RhABA0rBr1y6dPXtW1apVU+nSpRUREaE2bdqoQoUK2rZtmzp27KgpU6aoYMGCqlOnjlq0aCEvLy8lS5ZMyZIlk8SoWyR+GTNmVMaMGeMsCwwMVMeOHRUeHh5ngCcgMZ0L3qE8efLIw8NDTk5OSpkypRo1aqTVq1fr4MGD2r17t3x8fDR9+nRFRUWpUKFCWrp0qWbPnm0GhUBiU6JECa1evVouLi6SpJYtW2r8+PHy8vJS3759JUn58+fXhAkTtGLFCmXPnl3169fX8ePHZWtrq6ioKAJ0JEkx4fd3332nYcOGKVWqVOrRo4dSpUolFxcXnT59WunTp1e7du3UsWNHrVu3TtOmTbNyqYG3Y8GCBerSpYuaNWsmBwcHTZ06VePGjdPDhw916dKlOOv27t1bbm5u+v7777Vp06Y433ETCQCShqVLl6pTp066fv262a9u1qyZWrVqpeDgYI0fP14DBgxQz549VaBAAX322WfatGmT+vTpE2c/9DuQlDx8+FCTJk1Sx44d9eDBAx08eNDsgwMxmM4F79zly5fVv39/RUdHa/bs2cqbN68uX76sGTNmqE+fPipWrFic0bU8bo/ELvb5HhQUJC8vL/3www/65ptv5Orqaq4Xuy7wKCWSuitXrqhu3bpydXVVgwYNJElHjx7V+PHjderUKe3cuVNFihRRYGCg+YIg5n1GQrdixQp16tRJ3t7eqlevXpz2Y/jw4Zo2bZqWLVumb775Js5269evV6NGjWg3ACCJ8fLyUufOnbV06VLVrVtXqVOnjvP9zZs3VbVqVW3atEklSpSQv7+/hgwZor59+6pEiRJcOyHJOn36tH788Uflzp1b06ZNk52dHX1wvIYQHe/F1atX1bt3b0nSiBEjVLlyZfM7QnMkdTFB+ogRI9S6dWvNnDnT2kUCPjhnzpxR2bJltXv3blWpUsVcvnfvXjVv3lxp06bVmjVrVKpUKfO7qKgoOoNIsDw9PdWpUyfVrFlTu3btkvT6DdWhQ4dqxowZbwzS37Q+ACDxevDggb7++ms1b95cvXr1Mpc/f/5cFy5ckMViUd68eVW5cmXlzp1bAwcO1Lhx42QYhnbt2iUbGxuunZCkPX36VGnSpJHFYqEu4I1ILvFe5M2bV3PmzJGNjY0mTJigQ4cOmd8RoCOpS506tTm1y+zZszV79mxrFwmwqjfd38+TJ4/KlCmjHTt2KCQkxFxerVo1FS1aVEFBQRo9enSc7bnwRULl4eGhzp07q3Pnzjp//rz69esnSbKzs4vzWPGkSZM0cOBAde7cWYsXL35tPwToAJC0BAYGKlu2bOZnd3d3dezYUeXLl9eXX36pRo0aacyYMbpy5Yq6du2qsLAwbd++XTY2NoqOjubaCUmak5OTOb0wdQFvQnqJ9yZv3rxydXWVra2tBgwYoLNnz1q7SMAHI3Xq1Pr666+1ceNG86kNICmKjo42p6t4/Pixbt26JUlydHRUjRo1tHXrVq1Zs0YRERGSXj3JkS5dOnl6esrb21sSc3giYZs1a5a6d++uLVu2yMPDQyNHjtTq1avNIP3v83NOmjRJ7du314oVK6xVZADAByIoKEhbt241p7abN2+eMmTIoJ07d8rNzU0BAQG6ePGifvvtN23atEm+vr6yt7dXZGQkg9uA/4++BOLDdC547y5evKhFixZp6tSpNNRAPHgEH0lR7PmeR48erb179+rMmTNycXFR3bp11bFjR3Xs2FGnTp1Sjhw5VLZsWe3YsUOGYZgv/2GKMCR0+/fv1/3799WyZUtJ0rNnz7RmzRr98MMPatWqlfm00t8fM45dfwAASdPevXvVtGlTpU+fXqlSpdKMGTP0+eefK0OGDHry5ImcnZ315ZdfauzYseY2XDsBwH+HEB1WRYMNAJCkR48eKX369JKksWPHas6cOVq4cKEKFiyoTp06KSAgQPv371e2bNk0f/58+fr6ys/PTzly5JCnp6fs7e1pU5CoxPcS6thB+t9vuBKkAwACAwP1/Plz5cqVK87yJ0+eqFGjRmrTpo26detGmwEA/xAhOgAAsKqDBw+qcePGunjxolKnTq2qVavqxx9/VIMGDbR//365uLhozpw56tSpU5ztXrx4oY8++kgST28g8eMl1ACA/1VgYKA6duyohw8f6vDhw8z3DAD/A4ZrAQAAq8qaNasyZMig0aNH69GjR4qOjlblypW1adMmNWjQQDNmzFCnTp0UGhqqlStX6vLly5JkBuiGYRCgI9HjJdQAgH/q4cOHmjRpkjp27KgHDx6Y09/FfrcGAOC/Q48TAABYjWEYypkzp9q0aaP169dr3759CggIUL9+/fTrr79q8uTJ6tGjhyTpxo0bWrlypTJnzqz8+fOb++BRZCQVMS+hzpQpkxo0aGDt4gAAPnB3797V4cOHlSdPHm3atEl2dnY8vQcA/yOmcwEAAO/dxYsXVbBgQfPz06dPVa5cOdWtW1f16tVT06ZN1aRJE61YsUKGYSg0NFTNmzdXeHi4tm/fzmPIgJjGCADwnz19+lRp0qSRxWJ57aXUAID/HlfdAADgvdq8ebMaNWqkOnXqaO7cuUqXLp2cnJzk4eGh2rVrK1myZBo+fLhGjBghwzBkY2Oju3fvKjAwUH/88YdsbW15iSggEaADAP4jJycnSa+e/iNAB4D/Hb1PAADwXuXMmVPZs2fXkSNH1Lt3b7m6uurUqVOqUqWKunXrphMnTqhSpUry9vZWSEiI7Ozs5OzsrFOnTsne3l6RkZEE6AAAAP8A098BwP8N07kAAIB3LmbkeGRkpKKiojR79mwFBQXJyclJt27d0s6dOzVlyhSlSJFCvXr1UteuXfX9998rLCxMDg4O5n54DBkAAAAA8L4xjAsAALxz9+7dk/Rq+gkHBwcVL15chw4dUqlSpeTm5qYhQ4bo22+/1enTp5U+fXpNmTJF58+fNwP0mHv+BOgAAAAAgPeNEB0AALxTx48fV86cOTV48GBdvnxZklS7dm1VrlxZrVq10v3799WlSxd5e3srICBAjo6OevLkiebPn2/ug0eQAQAAAADWwnQuAADgnXr69KlWrFihsWPHqlChQqpTp46GDx8uSerQoYMcHR01adIkpUqVSk+ePNGNGzfk6empGTNm8OJEAAAAAIDVEaIDAID34sqVK5o0aZJ8fX2VJUsWubm56fTp0zp48KB69Oih8uXLm3Onx4iIiJC9vb0VSw0AAAAASOoI0QEAwHvz7NkznTp1SsOGDVNgYKDq1aunHTt2qGbNmpo3b561iwcAAAAAwGsI0QEAgFX88MMPOnfunA4cOKBnz55pw4YNaty4sbWLBQAAAABAHIToAADgvYo9ZcuxY8e0ZcsW7d69WwcPHmQOdAAAAADAB4cQHQAAvHeGYchisby2PDIykiAdAAAAAPBBIUQHAAAfhPiCdQAAAAAArMnG2gUAAACQRIAOAAAAAPggEaIDAAAAAAAAABAPQnQAAAAAAAAAAOJBiA4AAAAAAAAAQDwI0QEAAAAAAAAAiAchOgAAAAAAAAAA8SBEBwAAAAAAAAAgHoToAAAAAAAAAADEgxAdAAAASGIOHz6sokWLyt7eXo0bN7Z2ceKwWCzatGmTtYsBAAAAmAjRAQAAgLeoQ4cOslgsslgssre3V+bMmVWrVi0tWbJE0dHR1i6eJGngwIEqXry4bty4IU9Pz9e+Hzp0qAoWLBhn2cWLF2WxWNS2bds4y1esWCF7e3s9f/78XRYZAAAAsBpCdAAAAOAtq1u3ru7fv6+bN29q+/btcnZ2Vr9+/dSgQQNFRkZau3i6du2aqlevruzZs8vJyem1752dnXXp0iX5+/uby3x9fZUjRw75+PjEWdfX11dly5ZVypQp33WxAQAAAKsgRAcAAADeMgcHB2XJkkXZsmVTyZIlNXz4cHl7e2v79u1xRn7PmDFDRYsWlaOjo3LkyKGePXuaI7pDQkKUOnVqrVu3Ls6+N2/eLEdHRwUHB7/xb4eFhalv377KlCmTkidPrkqVKun48eOSpJs3b8pisejRo0fq1KmTLBbLG0eiV6pUSfb29vL19TWX+fr6qlevXgoODtZff/0VZ7mzs7Mk6dmzZ+rWrZsyZcqk1KlTq3r16jpz5sxr5S9VqpSSJ0+uzz77TGPGjPm3NxbGjh2rzJkz6/Tp0/GuAwAAALxLhOgAAADAe1C9enUVK1ZMGzZsMJfZ2NjI1dVV586d07Jly7Rv3z4NGTJEkuTo6KiWLVtq6dKlcfazdOlSNWvWTKlSpXrj3xkyZIjWr1+vZcuW6Y8//lCePHlUp04dPX78WDly5ND9+/eVOnVqzZo1S/fv31eLFi1e24ejo6PKlCkTZ9T5/v37VaNGDVWsWNFcfufOHV2/fl3Ozs4yDEP169eXv7+/tm3bppMnT6pkyZKqUaOGHj9+LEnauXOn2rRpo759++rChQtasGCBPD09NX78+NfKYBiG+vXrp8WLF+vQoUMqXrz4P/sPDgAAALwlhOgAAADAe1KgQAHdvHnT/Ny/f385OzsrV65cql69usaNG6dffvnF/L5Lly7auXOn/Pz8JEkPHz7Uli1b1KlTpzfuPyQkRO7u7po6dapcXFxUqFAheXh4KEWKFFq8eLFsbW2VJUsWWSwWpUmTRlmyZFGKFCneuK9q1aqZI9EvXLig0NBQlShRQlWrVjWX+/j4yMHBQRUqVJCPj4/+/PNPrV27VqVLl1bevHk1bdo0OTk5maPpx48fr6FDh6p9+/b67LPPVKtWLY0bN04LFiyI87cjIyPVrl077dq1S4cPH1bevHn/l//cAAAAwFtBiA4AAAC8J4ZhyGKxmJ99fHxUq1YtZcuWTalSpVK7du306NEjhYSESJLKli2rwoULa/ny5ZJevcTzk08+UZUqVd64/2vXrikiIkIVK1Y0l9nb26ts2bK6ePHiPyqrs7Ozrly5Ij8/P/n6+qpSpUqytbWNE6L7+vqqfPnySpEihU6ePKnnz58rffr0Spkypfnvxo0bunbtmiTp5MmTGjt2bJzvu3btqvv37+vFixfm3x4wYICOHj2qgwcPKnv27P+o3AAAAMDbRogOAAAAvCcXL15Urly5JEm3bt1SvXr1VKRIEa1fv14nT57U3LlzJUkRERHmNl26dDGndFm6dKk6duwYJ4iPzTAMSXrt+7+H9/+NihUrKlmyZPL19ZWPj4+qVq0qSSpdurSePXumK1euyMfHx5wPPTo6Wh9//LFOnz4d59/ly5c1ePBgc50xY8bE+f7PP//U1atXlTx5cvNv16pVS/fu3dPOnTv/UZkBAACAd4EQHQAAAHgP9u3bpz///FNNmzaVJJ04cUKRkZGaPn26ypcvr3z58pnTtsTWpk0b3b59W66urjp//rzat28f79/IkyePkiVLpkOHDpnLIiIidOLECRUsWPAflTdFihQqV66cfH19deDAAVWrVk2SZGdnpwoVKmj58uW6efOmGaKXLFlS/v7+srOzU548eeL8y5Ahg7nO5cuXX/s+T548srH5V9fkyy+/1OrVq9WlSxd5eXn9o3IDAAAAb5udtQsAAAAAJDZhYWHy9/dXVFSUAgICtGPHDk2cOFENGjRQu3btJEm5c+dWZGSk3Nzc1LBhQx0+fFjz589/bV9p06bVV199pcGDB6t27dr/dnoTR0dHffvttxo8eLDSpUunTz75RFOmTNGLFy/UuXPnf3wczs7OmjlzpqRXAXiMqlWravLkyWbQLkk1a9bUF198ocaNG2vy5MnKnz+//Pz8tG3bNjVu3FilS5fWyJEj1aBBA+XIkUNff/21bGxsdPbsWf3555/66aef4vztJk2aaMWKFWrbtq3s7OzUrFmzf1x+AAAA4G1gJDoAAADwlu3YsUMff/yxPv30U9WtW1c+Pj5ydXWVt7e3bG1tJUnFixfXjBkzNHnyZBUpUkSrVq3SxIkT37i/zp07Kzw8PN4XisY2adIkNW3aVG3btlXJkiX1119/aefOnUqbNu0/Pg5nZ2cFBwerYsWKsrP71/ibqlWrKjg4WBUqVJCDg4OkV1PIbNu2TVWqVFGnTp2UL18+tWzZUjdv3lTmzJklSXXq1NGWLVu0e/dulSlTRuXLl9eMGTOUM2fON/79Zs2aadmyZWrbtq02bNjwj8sPAAAAvA0WI2biRAAAAAAfpFWrVqlfv37y8/NTsmTJrF0cAAAAIElhOhcAAADgA/XixQvduHFDEydOVPfu3QnQAQAAACtgOhcAAADgAzVlyhQVL15cmTNn1rBhw6xdHAAAACBJYjoXAAAAAAAAAADiwUh0AAAAAAAAAADiQYgOAAAAAAAAAEA8CNEBAAAAAAAAAIgHIToAAAAAAAAAAPEgRAcAAAAAAAAAIB6E6AAAAAAAAAAAxIMQHQAAAAAAAACAeBCiAwAAAAAAAAAQj/8H2Fl4ETmoLIwAAAAASUVORK5CYII=", "text/plain": ["<Figure size 1500x1000 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Visualize temporal patterns\n", "fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 10))\n", "\n", "# Monthly activity trend\n", "monthly_activity.plot(kind='line', ax=ax1, marker='o', linewidth=2, markersize=6)\n", "ax1.set_title('Monthly Activity Trend')\n", "ax1.set_xlabel('Month')\n", "ax1.set_ylabel('Number of Activities')\n", "ax1.grid(True, alpha=0.3)\n", "\n", "# Day of week analysis\n", "df['day_of_week'] = df['encounter_start_date'].dt.day_name()\n", "day_order = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']\n", "daily_activity = df['day_of_week'].value_counts().reindex(day_order)\n", "daily_activity.plot(kind='bar', ax=ax2, color='lightblue')\n", "ax2.set_title('Activity by Day of Week')\n", "ax2.set_xlabel('Day of Week')\n", "ax2.set_ylabel('Number of Activities')\n", "ax2.tick_params(axis='x', rotation=45)\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 10. Data Quality Assessment"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔍 DATA QUALITY ASSESSMENT\n", "============================================================\n", "📊 MISSING DATA ANALYSIS:\n", "Fields with missing data:\n", "  accepted_type_reason_items: 4,912 (98.26%)\n", "  resub_type_desc: 4,651 (93.04%)\n", "  claim_status_desc: 4,292 (85.86%)\n", "  accepted_type: 4,281 (85.64%)\n", "  processing_status: 4,281 (85.64%)\n", "  denial_code: 4,122 (82.46%)\n", "  encounter_end_type: 4,117 (82.36%)\n", "  encounter_end_type_desc: 4,117 (82.36%)\n", "  resub_date: 3,853 (77.08%)\n", "  prior_authorization: 3,294 (65.89%)\n", "  activity_desc: 81 (1.62%)\n", "  clinician_name: 12 (0.24%)\n", "  id_payer: 6 (0.12%)\n", "  remittance_date: 6 (0.12%)\n", "\n", "✅ KEY FIELDS COMPLETENESS:\n", "  ✅ aio_patient_id: 100.0% complete\n", "  ✅ case: 100.0% complete\n", "  ✅ code_activity: 100.0% complete\n", "  ✅ encounter_start_date: 100.0% complete\n", "  ✅ type_activity: 100.0% complete\n", "\n", "🔍 DATA CONSISTENCY CHECKS:\n", "  ✅ Date consistency: 0 invalid date ranges\n", "  ✅ Financial consistency: 0 records with negative amounts\n", "  ✅ Code completeness: 0 missing activity codes\n"]}], "source": ["# Data quality assessment\n", "print(\"🔍 DATA QUALITY ASSESSMENT\")\n", "print(\"=\" * 60)\n", "\n", "# Missing data analysis\n", "missing_data = df.isnull().sum().sort_values(ascending=False)\n", "missing_percentage = (missing_data / len(df) * 100).round(2)\n", "\n", "print(\"📊 MISSING DATA ANALYSIS:\")\n", "print(\"Fields with missing data:\")\n", "for field, missing_count in missing_data[missing_data > 0].items():\n", "    percentage = missing_percentage[field]\n", "    print(f\"  {field}: {missing_count:,} ({percentage}%)\")\n", "\n", "# Key field completeness\n", "key_fields = ['aio_patient_id', 'case', 'code_activity', 'encounter_start_date', 'type_activity']\n", "print(f\"\\n✅ KEY FIELDS COMPLETENESS:\")\n", "for field in key_fields:\n", "    if field in df.columns:\n", "        completeness = (1 - df[field].isnull().sum() / len(df)) * 100\n", "        status = \"✅\" if completeness == 100 else \"⚠️\" if completeness >= 95 else \"❌\"\n", "        print(f\"  {status} {field}: {completeness:.1f}% complete\")\n", "\n", "# Data consistency checks\n", "print(f\"\\n🔍 DATA CONSISTENCY CHECKS:\")\n", "\n", "# Date consistency\n", "invalid_dates = (df['encounter_start_date'] > df['encounter_end_date']).sum()\n", "status = \"✅\" if invalid_dates == 0 else \"❌\"\n", "print(f\"  {status} Date consistency: {invalid_dates} invalid date ranges\")\n", "\n", "# Financial consistency\n", "negative_amounts = (df[['gross', 'net', 'payment_amount']] < 0).any(axis=1).sum()\n", "status = \"✅\" if negative_amounts == 0 else \"⚠️\"\n", "print(f\"  {status} Financial consistency: {negative_amounts} records with negative amounts\")\n", "\n", "# Code format consistency\n", "empty_codes = df['code_activity'].isnull().sum()\n", "status = \"✅\" if empty_codes == 0 else \"❌\"\n", "print(f\"  {status} Code completeness: {empty_codes} missing activity codes\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 11. OMOP Mapping Assessment"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🗺️ OMOP MAPPING ASSESSMENT\n", "============================================================\n", "📋 OMOP DOMAIN MAPPING POTENTIAL:\n", "\n", "👥 PERSON DOMAIN:\n", "  ✅ Unique patients: 596\n", "  ❌ Missing demographics: No age, gender, race data\n", "  📝 Strategy: Create basic Person records with unknown demographics\n", "\n", "🏥 VISIT_OCCURRENCE DOMAIN:\n", "  ✅ Unique encounters: 1,461\n", "  ✅ Date information: 4,999 records\n", "  ✅ Case types available: 3 types\n", "  📝 Strategy: Map case_type to visit_concept_id\n", "\n", "🔧 PROCEDURE_OCCURRENCE DOMAIN:\n", "  ✅ CPT records: 3,185 (63.7%)\n", "  ✅ Unique CPT codes: 297\n", "  📝 Strategy: Map CPT codes to OMOP procedure concepts\n", "  🎯 Expected mapping rate: >80% for standard CPT codes\n", "\n", "💊 DRUG_EXPOSURE DOMAIN:\n", "  ✅ Drug records: 1,154 (23.1%)\n", "  ✅ Unique drug codes: 428\n", "  ⚠️ Challenge: UAE-specific drug codes\n", "  📝 Strategy: Map to RxNorm where possible, use concept_id=0 for unmapped\n", "  🔗 Resource: Shafafiya Drug Dictionary available\n", "\n", "👨‍⚕️ PROVIDER DOMAIN:\n", "  ✅ Unique providers: 10\n", "  ✅ Unique clinicians: 270\n", "  ❌ Missing specialty information\n", "  📝 Strategy: Create basic Provider records\n", "\n", "🏢 PAYER DOMAIN:\n", "  ✅ Unique payers: 6\n", "  ✅ Insurance plans: 9\n", "  📝 Strategy: Map UAE payers to standard concepts\n"]}], "source": ["# OMOP mapping assessment\n", "print(\"🗺️ OMOP MAPPING ASSESSMENT\")\n", "print(\"=\" * 60)\n", "\n", "# Domain mapping potential\n", "print(\"📋 OMOP DOMAIN MAPPING POTENTIAL:\")\n", "\n", "# Person domain\n", "unique_patients = df['aio_patient_id'].nunique()\n", "print(f\"\\n👥 PERSON DOMAIN:\")\n", "print(f\"  ✅ Unique patients: {unique_patients:,}\")\n", "print(f\"  ❌ Missing demographics: No age, gender, race data\")\n", "print(f\"  📝 Strategy: Create basic Person records with unknown demographics\")\n", "\n", "# Visit_Occurrence domain\n", "unique_encounters = df['case'].nunique()\n", "print(f\"\\n🏥 VISIT_OCCURRENCE DOMAIN:\")\n", "print(f\"  ✅ Unique encounters: {unique_encounters:,}\")\n", "print(f\"  ✅ Date information: {(df['encounter_start_date'].notna()).sum():,} records\")\n", "print(f\"  ✅ Case types available: {df['case_type'].nunique()} types\")\n", "print(f\"  📝 Strategy: Map case_type to visit_concept_id\")\n", "\n", "# Procedure_Occurrence domain\n", "cpt_records = len(df[df['type_activity'] == 3])\n", "cpt_coverage = cpt_records / len(df) * 100\n", "print(f\"\\n🔧 PROCEDURE_OCCURRENCE DOMAIN:\")\n", "print(f\"  ✅ CPT records: {cpt_records:,} ({cpt_coverage:.1f}%)\")\n", "print(f\"  ✅ Unique CPT codes: {df[df['type_activity'] == 3]['code_activity'].nunique():,}\")\n", "print(f\"  📝 Strategy: Map CPT codes to OMOP procedure concepts\")\n", "print(f\"  🎯 Expected mapping rate: >80% for standard CPT codes\")\n", "\n", "# Drug_Exposure domain\n", "drug_records = len(df[df['type_activity'] == 5])\n", "drug_coverage = drug_records / len(df) * 100\n", "print(f\"\\n💊 DRUG_EXPOSURE DOMAIN:\")\n", "print(f\"  ✅ Drug records: {drug_records:,} ({drug_coverage:.1f}%)\")\n", "print(f\"  ✅ Unique drug codes: {df[df['type_activity'] == 5]['code_activity'].nunique():,}\")\n", "print(f\"  ⚠️ Challenge: UAE-specific drug codes\")\n", "print(f\"  📝 Strategy: Map to RxNorm where possible, use concept_id=0 for unmapped\")\n", "print(f\"  🔗 Resource: Shafafiya Drug Dictionary available\")\n", "\n", "# Provider domain\n", "unique_providers = df['provider_id'].nunique()\n", "unique_clinicians = df['clinician'].nunique()\n", "print(f\"\\n👨‍⚕️ PROVIDER DOMAIN:\")\n", "print(f\"  ✅ Unique providers: {unique_providers:,}\")\n", "print(f\"  ✅ Unique clinicians: {unique_clinicians:,}\")\n", "print(f\"  ❌ Missing specialty information\")\n", "print(f\"  📝 Strategy: Create basic Provider records\")\n", "\n", "# Payer domain\n", "unique_payers = df['payer_id'].nunique()\n", "print(f\"\\n🏢 PAYER DOMAIN:\")\n", "print(f\"  ✅ Unique payers: {unique_payers:,}\")\n", "print(f\"  ✅ Insurance plans: {df['insurance_plan_id'].nunique():,}\")\n", "print(f\"  📝 Strategy: Map UAE payers to standard concepts\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 12. Identified Limitations and Challenges"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["⚠️ IDENTIFIED LIMITATIONS AND CHALLENGES\n", "============================================================\n", "\n", "🔍 This is an INITIAL DATASET from the Abu Dhabi client.\n", "The following limitations need to be discussed with the team:\n", "\n", "❌ CRITICAL MISSING DATA:\n", "  1. Patient Demographics:\n", "     - No age/date of birth information\n", "     - No gender information\n", "     - No race/ethnicity data\n", "     - Impact: Cannot create complete Person records\n", "\n", "  2. Clinical Context:\n", "     - No explicit diagnosis codes (ICD-10)\n", "     - Limited clinical specialty information\n", "     - No severity or urgency indicators\n", "     - Impact: Limited clinical analysis capabilities\n", "\n", "  3. Provider Information:\n", "     - No provider specialty codes\n", "     - Limited facility information\n", "     - No provider credentials or qualifications\n", "     - Impact: Cannot create detailed Provider records\n", "\n", "⚠️ VOCABULARY CHALLENGES:\n", "  1. Local Drug Codes:\n", "     - UAE-specific format: B46-4387-00778-01\n", "     - Not standard RxNorm or NDC\n", "     - Requires Shafafiya Dictionary mapping\n", "     - Impact: Complex drug concept mapping\n", "\n", "  2. Mixed Code Systems:\n", "     - Multiple activity types with different coding systems\n", "     - Some non-standard CPT variations\n", "     - Local procedure codes mixed with standard CPT\n", "     - Impact: Requires multiple vocabulary sources\n", "\n", "🔧 DATA QUALITY ISSUES:\n", "  1. High Missing Data Fields:\n", "     - accepted_type_reason_items: 98.3% missing\n", "     - resub_type_desc: 93.0% missing\n", "     - claim_status_desc: 85.9% missing\n", "     - accepted_type: 85.6% missing\n", "     - processing_status: 85.6% missing\n", "     - denial_code: 82.5% missing\n", "     - encounter_end_type: 82.4% missing\n", "     - encounter_end_type_desc: 82.4% missing\n", "     - resub_date: 77.1% missing\n", "     - prior_authorization: 65.9% missing\n", "\n", "  2. Financial Complexity:\n", "     - 1,583 partially rejected claims (31.7%)\n", "     - Complex denial code system\n", "     - Multiple payment scenarios\n", "     - Impact: Requires sophisticated financial logic\n"]}], "source": ["# Comprehensive limitations assessment\n", "print(\"⚠️ IDENTIFIED LIMITATIONS AND CHALLENGES\")\n", "print(\"=\" * 60)\n", "print(\"\\n🔍 This is an INITIAL DATASET from the Abu Dhabi client.\")\n", "print(\"The following limitations need to be discussed with the team:\")\n", "\n", "print(\"\\n❌ CRITICAL MISSING DATA:\")\n", "print(\"  1. Patient Demographics:\")\n", "print(\"     - No age/date of birth information\")\n", "print(\"     - No gender information\")\n", "print(\"     - No race/ethnicity data\")\n", "print(\"     - Impact: Cannot create complete Person records\")\n", "\n", "print(\"\\n  2. Clinical Context:\")\n", "print(\"     - No explicit diagnosis codes (ICD-10)\")\n", "print(\"     - Limited clinical specialty information\")\n", "print(\"     - No severity or urgency indicators\")\n", "print(\"     - Impact: Limited clinical analysis capabilities\")\n", "\n", "print(\"\\n  3. Provider Information:\")\n", "print(\"     - No provider specialty codes\")\n", "print(\"     - Limited facility information\")\n", "print(\"     - No provider credentials or qualifications\")\n", "print(\"     - Impact: Cannot create detailed Provider records\")\n", "\n", "print(\"\\n⚠️ VOCABULARY CHALLENGES:\")\n", "print(\"  1. Local Drug Codes:\")\n", "print(f\"     - UAE-specific format: {df[df['type_activity']==5]['code_activity'].iloc[0] if len(df[df['type_activity']==5]) > 0 else 'N/A'}\")\n", "print(\"     - Not standard RxNorm or NDC\")\n", "print(\"     - Requires Shafafiya Dictionary mapping\")\n", "print(\"     - Impact: Complex drug concept mapping\")\n", "\n", "print(\"\\n  2. Mixed Code Systems:\")\n", "print(\"     - Multiple activity types with different coding systems\")\n", "print(\"     - Some non-standard CPT variations\")\n", "print(\"     - Local procedure codes mixed with standard CPT\")\n", "print(\"     - Impact: Requires multiple vocabulary sources\")\n", "\n", "print(\"\\n🔧 DATA QUALITY ISSUES:\")\n", "missing_summary = df.isnull().sum().sort_values(ascending=False)\n", "critical_missing = missing_summary[missing_summary > len(df) * 0.1]  # >10% missing\n", "if len(critical_missing) > 0:\n", "    print(\"  1. High Missing Data Fields:\")\n", "    for field, missing_count in critical_missing.items():\n", "        percentage = (missing_count / len(df)) * 100\n", "        print(f\"     - {field}: {percentage:.1f}% missing\")\n", "\n", "print(\"\\n  2. Financial Complexity:\")\n", "partial_rejected = (df['claim_mapping_status'] == 'Partially Rejected').sum()\n", "print(f\"     - {partial_rejected:,} partially rejected claims ({partial_rejected/len(df)*100:.1f}%)\")\n", "print(\"     - Complex denial code system\")\n", "print(\"     - Multiple payment scenarios\")\n", "print(\"     - Impact: Requires sophisticated financial logic\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 13. Recommendations for Client Discussion"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["💡 RECOMMENDATIONS FOR CLIENT DISCUSSION\n", "============================================================\n", "\n", "🎯 IMMEDIATE PRIORITIES:\n", "  1. Patient Demographics Enhancement:\n", "     - Request age/date of birth data\n", "     - Request gender information\n", "     - Discuss privacy constraints for demographic data\n", "     - Alternative: Age groups instead of exact ages\n", "\n", "  2. Clinical Context Enrichment:\n", "     - Request diagnosis codes (ICD-10) if available\n", "     - Request provider specialty information\n", "     - Request clinical severity indicators\n", "     - Clarify clinical workflow context\n", "\n", "  3. Vocabulary Mapping Support:\n", "     - Confirm access to Shafafiya Dictionary\n", "     - Request drug code mapping tables\n", "     - Clarify local vs. international code usage\n", "     - Request procedure code documentation\n", "\n", "📋 DATA QUALITY IMPROVEMENTS:\n", "  1. Field Completeness:\n", "     - Address high missing data fields\n", "     - Clarify business rules for optional fields\n", "     - Request data validation rules\n", "\n", "  2. Standardization Opportunities:\n", "     - Discuss migration to international standards\n", "     - Evaluate FHIR compatibility\n", "     - Plan vocabulary harmonization\n", "\n", "🚀 IMPLEMENTATION STRATEGY:\n", "  1. Phased Approach:\n", "     - Phase 1: Work with current data limitations\n", "     - Phase 2: Incorporate enhanced data when available\n", "     - Phase 3: Full OMOP compliance with complete data\n", "\n", "  2. MVP <PERSON> Adjustment:\n", "     - Focus on available data domains\n", "     - Document mapping limitations\n", "     - Create extensible architecture for future enhancements\n", "     - Establish data quality metrics\n"]}], "source": ["# Recommendations for client discussion\n", "print(\"💡 RECOMMENDATIONS FOR CLIENT DISCUSSION\")\n", "print(\"=\" * 60)\n", "\n", "print(\"\\n🎯 IMMEDIATE PRIORITIES:\")\n", "print(\"  1. Patient Demographics Enhancement:\")\n", "print(\"     - Request age/date of birth data\")\n", "print(\"     - Request gender information\")\n", "print(\"     - Discuss privacy constraints for demographic data\")\n", "print(\"     - Alternative: Age groups instead of exact ages\")\n", "\n", "print(\"\\n  2. Clinical Context Enrichment:\")\n", "print(\"     - Request diagnosis codes (ICD-10) if available\")\n", "print(\"     - Request provider specialty information\")\n", "print(\"     - Request clinical severity indicators\")\n", "print(\"     - Clarify clinical workflow context\")\n", "\n", "print(\"\\n  3. Vocabulary Mapping Support:\")\n", "print(\"     - Confirm access to Shafafiya Dictionary\")\n", "print(\"     - Request drug code mapping tables\")\n", "print(\"     - Clarify local vs. international code usage\")\n", "print(\"     - Request procedure code documentation\")\n", "\n", "print(\"\\n📋 DATA QUALITY IMPROVEMENTS:\")\n", "print(\"  1. Field Completeness:\")\n", "print(\"     - Address high missing data fields\")\n", "print(\"     - Clarify business rules for optional fields\")\n", "print(\"     - Request data validation rules\")\n", "\n", "print(\"\\n  2. Standardization Opportunities:\")\n", "print(\"     - Discuss migration to international standards\")\n", "print(\"     - Evaluate FHIR compatibility\")\n", "print(\"     - Plan vocabulary harmonization\")\n", "\n", "print(\"\\n🚀 IMPLEMENTATION STRATEGY:\")\n", "print(\"  1. Phased Approach:\")\n", "print(\"     - Phase 1: Work with current data limitations\")\n", "print(\"     - Phase 2: Incorporate enhanced data when available\")\n", "print(\"     - Phase 3: Full OMOP compliance with complete data\")\n", "\n", "print(\"\\n  2. <PERSON> <PERSON>ope Adjustment:\")\n", "print(\"     - Focus on available data domains\")\n", "print(\"     - Document mapping limitations\")\n", "print(\"     - Create extensible architecture for future enhancements\")\n", "print(\"     - Establish data quality metrics\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 14. Final Analysis Summary"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📊 FINAL ANALYSIS SUMMARY\n", "============================================================\n", "\n", "📈 DATASET METRICS:\n", "  Total records: 4,999\n", "  Unique patients: 596\n", "  Unique encounters: 1,461\n", "  Date coverage: 364 days\n", "  Primary institution: BDSC\n", "  Dominant payer: Daman Insurance\n", "\n", "🎯 OMOP READINESS ASSESSMENT:\n", "  Person Domain: 40% ready\n", "  Visit_Occurrence Domain: 85% ready\n", "  Procedure_Occurrence Domain: 75% ready\n", "  Drug_Exposure Domain: 60% ready\n", "  Provider Domain: 50% ready\n", "  Overall OMOP Readiness: 62%\n", "\n", "✅ SUCCESS FACTORS:\n", "  - Real-world healthcare data with authentic patterns\n", "  - Comprehensive claims workflow representation\n", "  - Good temporal coverage (full year 2023)\n", "  - Manageable dataset size for MVP development\n", "  - Clear activity type categorization\n", "  - Financial data for cost analysis\n", "\n", "⚠️ RISK FACTORS:\n", "  - Missing critical demographic data\n", "  - Local vocabulary systems requiring mapping\n", "  - Complex claims status and denial logic\n", "  - Limited clinical context information\n", "  - Provider specialty information gaps\n", "\n", "🎯 FINAL RECOMMENDATION:\n", "  PROCEED with MVP development using current dataset\n", "  DOCUMENT all limitations for client discussion\n", "  IMPLEMENT flexible architecture for future enhancements\n", "  FOCUS on demonstrating OMOP value with available data\n", "  ESTABLISH foundation for iterative improvement\n", "\n", "============================================================\n", "📋 EDA COMPLETE - Ready for OMOP MVP Implementation\n", "============================================================\n"]}], "source": ["# Final comprehensive summary\n", "print(\"📊 FINAL ANALYSIS SUMMARY\")\n", "print(\"=\" * 60)\n", "\n", "# Dataset metrics\n", "print(\"\\n📈 DATASET METRICS:\")\n", "print(f\"  Total records: {len(df):,}\")\n", "print(f\"  Unique patients: {df['aio_patient_id'].nunique():,}\")\n", "print(f\"  Unique encounters: {df['case'].nunique():,}\")\n", "print(f\"  Date coverage: {(df['encounter_start_date'].max() - df['encounter_start_date'].min()).days} days\")\n", "print(f\"  Primary institution: {df['institution_name'].value_counts().index[0]}\")\n", "print(f\"  Dominant payer: {df['payer_id_desc'].value_counts().index[0]}\")\n", "\n", "# OMOP readiness assessment\n", "print(\"\\n🎯 OMOP READINESS ASSESSMENT:\")\n", "\n", "# Calculate readiness scores\n", "person_readiness = 40  # Limited due to missing demographics\n", "visit_readiness = 85   # Good encounter data\n", "procedure_readiness = 75  # Good CPT coverage\n", "drug_readiness = 60   # Local codes challenge\n", "provider_readiness = 50  # Limited provider info\n", "\n", "print(f\"  Person Domain: {person_readiness}% ready\")\n", "print(f\"  Visit_Occurrence Domain: {visit_readiness}% ready\")\n", "print(f\"  Procedure_Occurrence Domain: {procedure_readiness}% ready\")\n", "print(f\"  Drug_Exposure Domain: {drug_readiness}% ready\")\n", "print(f\"  Provider Domain: {provider_readiness}% ready\")\n", "print(f\"  Overall OMOP Readiness: {(person_readiness + visit_readiness + procedure_readiness + drug_readiness + provider_readiness) / 5:.0f}%\")\n", "\n", "# Success factors\n", "print(\"\\n✅ SUCCESS FACTORS:\")\n", "print(\"  - Real-world healthcare data with authentic patterns\")\n", "print(\"  - Comprehensive claims workflow representation\")\n", "print(\"  - Good temporal coverage (full year 2023)\")\n", "print(\"  - Manageable dataset size for MVP development\")\n", "print(\"  - Clear activity type categorization\")\n", "print(\"  - Financial data for cost analysis\")\n", "\n", "# Risk factors\n", "print(\"\\n⚠️ RISK FACTORS:\")\n", "print(\"  - Missing critical demographic data\")\n", "print(\"  - Local vocabulary systems requiring mapping\")\n", "print(\"  - Complex claims status and denial logic\")\n", "print(\"  - Limited clinical context information\")\n", "print(\"  - Provider specialty information gaps\")\n", "\n", "# Final recommendation\n", "print(\"\\n🎯 FINAL RECOMMENDATION:\")\n", "print(\"  PROCEED with MVP development using current dataset\")\n", "print(\"  DOCUMENT all limitations for client discussion\")\n", "print(\"  IMPLEMENT flexible architecture for future enhancements\")\n", "print(\"  FOCUS on demonstrating OMOP value with available data\")\n", "print(\"  ESTABLISH foundation for iterative improvement\")\n", "\n", "print(\"\\n\" + \"=\" * 60)\n", "print(\"📋 EDA COMPLETE - Ready for OMOP MVP Implementation\")\n", "print(\"=\" * 60)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 15. Next Steps\n", "\n", "### Immediate Actions\n", "1. **Review findings with team** - <PERSON><PERSON> identified limitations and recommendations\n", "2. **Client communication** - Present analysis and request additional data elements\n", "3. **Vocabulary research** - Access Shafafiya Dictionary for code mapping\n", "4. **Architecture planning** - Design flexible OMOP implementation\n", "\n", "### Implementation Priorities\n", "1. **Phase 1**: Basic OMOP structure with available data\n", "2. **Phase 2**: Enhanced mapping with vocabulary resources\n", "3. **Phase 3**: Integration of additional data when available\n", "4. **Phase 4**: Full OMOP compliance and optimization\n", "\n", "### Success Metrics\n", "- **≥80% CPT code mapping** success rate\n", "- **100% patient and encounter** mapping\n", "- **Comprehensive documentation** of limitations\n", "- **Extensible architecture** for future enhancements\n", "- **Clear value demonstration** to client\n", "\n", "---\n", "\n", "**Analysis completed**: This EDA provides the foundation for informed OMOP MVP development with realistic expectations and clear improvement pathways."]}], "metadata": {"kernelspec": {"display_name": "fhir-omop", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}