{"cells": [{"cell_type": "markdown", "id": "22f91bc4", "metadata": {}, "source": ["# Abu Dhabi Claims Dataset - Exploratory Data Analysis\n", "\n", "## Overview\n", "This notebook provides a systematic analysis of the Abu Dhabi healthcare claims dataset to understand:\n", "- **Data structure and quality**\n", "- **Patient and encounter patterns** \n", "- **Medical codes and procedures**\n", "- **Financial information**\n", "- **Potential for OMOP CDM mapping**\n", "\n", "## Goal\n", "Gain clear insights into our data to plan the FHIR-to-OMOP transformation strategy."]}, {"cell_type": "code", "execution_count": 21, "id": "aabc9593", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Libraries loaded successfully\n", "📅 Analysis started: 2025-05-28 16:36:53\n"]}], "source": ["# Import required libraries\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from datetime import datetime\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Configure display options\n", "pd.set_option('display.max_columns', None)\n", "pd.set_option('display.max_rows', 100)\n", "plt.style.use('default')\n", "sns.set_palette('husl')\n", "\n", "print(\"✅ Libraries loaded successfully\")\n", "print(f\"📅 Analysis started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")"]}, {"cell_type": "markdown", "id": "23ba3dd1", "metadata": {}, "source": ["## 1. <PERSON><PERSON> and First Look at the Data"]}, {"cell_type": "code", "execution_count": 2, "id": "b3bf1877", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📊 DATASET OVERVIEW\n", "==================================================\n", "Total records: 4,999\n", "Total columns: 54\n", "Memory usage: 11.3 MB\n", "Dataset period: 2023 (Abu Dhabi)\n"]}], "source": ["# Load the dataset\n", "file_path = '/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Work/AIO/fhir-omop/data/real_test_datasets/claim_anonymized.csv'\n", "df = pd.read_csv(file_path)\n", "\n", "print(\"📊 DATASET OVERVIEW\")\n", "print(\"=\" * 50)\n", "print(f\"Total records: {len(df):,}\")\n", "print(f\"Total columns: {df.shape[1]}\")\n", "print(f\"Memory usage: {df.memory_usage(deep=True).sum() / 1024**2:.1f} MB\")\n", "print(f\"Dataset period: 2023 (Abu Dhabi)\")"]}, {"cell_type": "code", "execution_count": 3, "id": "d4ef38ef", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📋 COLUMN INFORMATION\n", "==================================================\n", "Column names:\n", " 1. provider_id (object)\n", " 2. institution_name (object)\n", " 3. case_type (object)\n", " 4. claim_id (int64)\n", " 5. claim_net (float64)\n", " 6. unique_id (object)\n", " 7. case (int64)\n", " 8. insurance_plan_id (int64)\n", " 9. plan_name (object)\n", "10. network_name (object)\n", "11. payer_id (object)\n", "12. payer_id_desc (object)\n", "13. id_payer (object)\n", "14. denial_code (object)\n", "15. code_activity (object)\n", "16. activity_desc (object)\n", "17. activity_id (int64)\n", "18. reference_activity (object)\n", "19. start_activity_date (object)\n", "20. type_activity (int64)\n", "21. act_type_desc (object)\n", "22. activity_quantity (float64)\n", "23. mapping_status (object)\n", "24. claim_mapping_status (object)\n", "25. gross (float64)\n", "26. patient_share (float64)\n", "27. net (float64)\n", "28. payment_amount (float64)\n", "29. rejected_amount (float64)\n", "30. resub_net (float64)\n", "31. clinician (object)\n", "32. clinician_name (object)\n", "33. resub_date (object)\n", "34. remittance_date (object)\n", "35. ra_aging (int64)\n", "36. resub_aging (int64)\n", "37. claim_status_desc (object)\n", "38. resub_type_desc (object)\n", "39. encounter_start_type (int64)\n", "40. encounter_start_type_desc (object)\n", "41. encounter_start_date (object)\n", "42. encounter_end_date (object)\n", "43. encounter_end_type (float64)\n", "44. encounter_end_type_desc (object)\n", "45. receiver_id (object)\n", "46. receiver_id_desc (object)\n", "47. prior_authorization (object)\n", "48. submission_date (object)\n", "49. processing_status (object)\n", "50. accepted_type (object)\n", "51. accepted_type_reason_items (object)\n", "52. reconciliation_claim_tag (object)\n", "53. year_encounter_end_date (int64)\n", "54. aio_patient_id (object)\n"]}], "source": ["# Check column names and data types\n", "print(\"📋 COLUMN INFORMATION\")\n", "print(\"=\" * 50)\n", "print(f\"Column names:\")\n", "for i, col in enumerate(df.columns, 1):\n", "    print(f\"{i:2d}. {col} ({df[col].dtype})\")"]}, {"cell_type": "code", "execution_count": 22, "id": "674ae3a9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["👀 SAMPLE DATA (First 3 rows)\n", "==================================================\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>0</th>\n", "      <th>1</th>\n", "      <th>2</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>provider_id</th>\n", "      <td>MF4252</td>\n", "      <td>MF4252</td>\n", "      <td>MF4252</td>\n", "    </tr>\n", "    <tr>\n", "      <th>institution_name</th>\n", "      <td>BDSC</td>\n", "      <td>BDSC</td>\n", "      <td>BDSC</td>\n", "    </tr>\n", "    <tr>\n", "      <th>case_type</th>\n", "      <td>Outpatient Case</td>\n", "      <td>Outpatient Case</td>\n", "      <td>Outpatient Case</td>\n", "    </tr>\n", "    <tr>\n", "      <th>claim_id</th>\n", "      <td>**********</td>\n", "      <td>**********</td>\n", "      <td>**********</td>\n", "    </tr>\n", "    <tr>\n", "      <th>claim_net</th>\n", "      <td>221.0</td>\n", "      <td>221.0</td>\n", "      <td>92.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>unique_id</th>\n", "      <td>MF4252**********</td>\n", "      <td>MF4252**********</td>\n", "      <td>MF4252**********</td>\n", "    </tr>\n", "    <tr>\n", "      <th>case</th>\n", "      <td>**********</td>\n", "      <td>**********</td>\n", "      <td>**********</td>\n", "    </tr>\n", "    <tr>\n", "      <th>insurance_plan_id</th>\n", "      <td>700000</td>\n", "      <td>700000</td>\n", "      <td>700000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>plan_name</th>\n", "      <td>COMPREHENSIVE 3 - ALDAR</td>\n", "      <td>COMPREHENSIVE 3 - ALDAR</td>\n", "      <td>COMPREHENSIVE 3 - ALDAR</td>\n", "    </tr>\n", "    <tr>\n", "      <th>network_name</th>\n", "      <td>ALDAR-COMP 3</td>\n", "      <td>ALDAR-COMP 3</td>\n", "      <td>ALDAR-COMP 3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>payer_id</th>\n", "      <td>A001</td>\n", "      <td>A001</td>\n", "      <td>A001</td>\n", "    </tr>\n", "    <tr>\n", "      <th>payer_id_desc</th>\n", "      <td>Daman Insurance</td>\n", "      <td>Daman Insurance</td>\n", "      <td>Daman Insurance</td>\n", "    </tr>\n", "    <tr>\n", "      <th>id_payer</th>\n", "      <td>2.12E+11</td>\n", "      <td>2.12E+11</td>\n", "      <td>2.12E+11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>denial_code</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>code_activity</th>\n", "      <td>87880</td>\n", "      <td>99203</td>\n", "      <td>99203</td>\n", "    </tr>\n", "    <tr>\n", "      <th>activity_desc</th>\n", "      <td>Group A Streptococcus Antigen, Throat Swab</td>\n", "      <td>Office or other outpatient visit for the evalu...</td>\n", "      <td>Office or other outpatient visit for the evalu...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>activity_id</th>\n", "      <td>**********</td>\n", "      <td>**********</td>\n", "      <td>**********</td>\n", "    </tr>\n", "    <tr>\n", "      <th>reference_activity</th>\n", "      <td>154527198</td>\n", "      <td>154682709</td>\n", "      <td>154658090</td>\n", "    </tr>\n", "    <tr>\n", "      <th>start_activity_date</th>\n", "      <td>16/06/2023</td>\n", "      <td>16/06/2023</td>\n", "      <td>16/06/2023</td>\n", "    </tr>\n", "    <tr>\n", "      <th>type_activity</th>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>act_type_desc</th>\n", "      <td>CPT</td>\n", "      <td>CPT</td>\n", "      <td>CPT</td>\n", "    </tr>\n", "    <tr>\n", "      <th>activity_quantity</th>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mapping_status</th>\n", "      <td><PERSON><PERSON>id</td>\n", "      <td><PERSON><PERSON>id</td>\n", "      <td><PERSON><PERSON>id</td>\n", "    </tr>\n", "    <tr>\n", "      <th>claim_mapping_status</th>\n", "      <td><PERSON><PERSON>id</td>\n", "      <td><PERSON><PERSON>id</td>\n", "      <td><PERSON><PERSON>id</td>\n", "    </tr>\n", "    <tr>\n", "      <th>gross</th>\n", "      <td>43.0</td>\n", "      <td>142.0</td>\n", "      <td>142.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>patient_share</th>\n", "      <td>0.0</td>\n", "      <td>50.0</td>\n", "      <td>50.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>net</th>\n", "      <td>43.0</td>\n", "      <td>92.0</td>\n", "      <td>92.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>payment_amount</th>\n", "      <td>43.0</td>\n", "      <td>92.0</td>\n", "      <td>92.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>rejected_amount</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>resub_net</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>clinician</th>\n", "      <td>GD11650</td>\n", "      <td>GD11650</td>\n", "      <td>GD25783</td>\n", "    </tr>\n", "    <tr>\n", "      <th>clinician_name</th>\n", "      <td>PRASANNA SHETTY</td>\n", "      <td>PRASANNA SHETTY</td>\n", "      <td><PERSON><PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>resub_date</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>remittance_date</th>\n", "      <td>07/10/2023</td>\n", "      <td>07/10/2023</td>\n", "      <td>28/07/2023</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ra_aging</th>\n", "      <td>110</td>\n", "      <td>110</td>\n", "      <td>39</td>\n", "    </tr>\n", "    <tr>\n", "      <th>resub_aging</th>\n", "      <td>564</td>\n", "      <td>564</td>\n", "      <td>635</td>\n", "    </tr>\n", "    <tr>\n", "      <th>claim_status_desc</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>resub_type_desc</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>encounter_start_type</th>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>encounter_start_type_desc</th>\n", "      <td>Elective, i.e., an Encounter is schedule</td>\n", "      <td>Elective, i.e., an Encounter is schedule</td>\n", "      <td>Elective, i.e., an Encounter is schedule</td>\n", "    </tr>\n", "    <tr>\n", "      <th>encounter_start_date</th>\n", "      <td>16/06/2023</td>\n", "      <td>16/06/2023</td>\n", "      <td>16/06/2023</td>\n", "    </tr>\n", "    <tr>\n", "      <th>encounter_end_date</th>\n", "      <td>16/06/2023</td>\n", "      <td>16/06/2023</td>\n", "      <td>16/06/2023</td>\n", "    </tr>\n", "    <tr>\n", "      <th>encounter_end_type</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>encounter_end_type_desc</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>receiver_id</th>\n", "      <td>A001</td>\n", "      <td>A001</td>\n", "      <td>A001</td>\n", "    </tr>\n", "    <tr>\n", "      <th>receiver_id_desc</th>\n", "      <td>Daman Insurance</td>\n", "      <td>Daman Insurance</td>\n", "      <td>Daman Insurance</td>\n", "    </tr>\n", "    <tr>\n", "      <th>prior_authorization</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>submission_date</th>\n", "      <td>19/06/2023</td>\n", "      <td>19/06/2023</td>\n", "      <td>19/06/2023</td>\n", "    </tr>\n", "    <tr>\n", "      <th>processing_status</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>accepted_type</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>accepted_type_reason_items</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>reconciliation_claim_tag</th>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "    </tr>\n", "    <tr>\n", "      <th>year_encounter_end_date</th>\n", "      <td>2023</td>\n", "      <td>2023</td>\n", "      <td>2023</td>\n", "    </tr>\n", "    <tr>\n", "      <th>aio_patient_id</th>\n", "      <td>AIO00001</td>\n", "      <td>AIO00001</td>\n", "      <td>AIO00002</td>\n", "    </tr>\n", "    <tr>\n", "      <th>is_numeric_5digit</th>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                                                     0  \\\n", "provider_id                                                     MF4252   \n", "institution_name                                                  BDSC   \n", "case_type                                              Outpatient Case   \n", "claim_id                                                    **********   \n", "claim_net                                                        221.0   \n", "unique_id                                             MF4252**********   \n", "case                                                        **********   \n", "insurance_plan_id                                               700000   \n", "plan_name                                      COMPREHENSIVE 3 - ALDAR   \n", "network_name                                              ALDAR-COMP 3   \n", "payer_id                                                          A001   \n", "payer_id_desc                                          Daman Insurance   \n", "id_payer                                                      2.12E+11   \n", "denial_code                                                        NaN   \n", "code_activity                                                    87880   \n", "activity_desc               Group A Streptococcus Antigen, Throat Swab   \n", "activity_id                                                 **********   \n", "reference_activity                                           154527198   \n", "start_activity_date                                         16/06/2023   \n", "type_activity                                                        3   \n", "act_type_desc                                                      CPT   \n", "activity_quantity                                                  1.0   \n", "mapping_status                                              Fully Paid   \n", "claim_mapping_status                                        Fully Paid   \n", "gross                                                             43.0   \n", "patient_share                                                      0.0   \n", "net                                                               43.0   \n", "payment_amount                                                    43.0   \n", "rejected_amount                                                    0.0   \n", "resub_net                                                          0.0   \n", "clinician                                                      GD11650   \n", "clinician_name                                         PRASANNA SHETTY   \n", "resub_date                                                         NaN   \n", "remittance_date                                             07/10/2023   \n", "ra_aging                                                           110   \n", "resub_aging                                                        564   \n", "claim_status_desc                                                  NaN   \n", "resub_type_desc                                                    NaN   \n", "encounter_start_type                                                 1   \n", "encounter_start_type_desc     Elective, i.e., an Encounter is schedule   \n", "encounter_start_date                                        16/06/2023   \n", "encounter_end_date                                          16/06/2023   \n", "encounter_end_type                                                 NaN   \n", "encounter_end_type_desc                                            NaN   \n", "receiver_id                                                       A001   \n", "receiver_id_desc                                       Daman Insurance   \n", "prior_authorization                                                NaN   \n", "submission_date                                             19/06/2023   \n", "processing_status                                                  NaN   \n", "accepted_type                                                      NaN   \n", "accepted_type_reason_items                                         NaN   \n", "reconciliation_claim_tag                                            No   \n", "year_encounter_end_date                                           2023   \n", "aio_patient_id                                                AIO00001   \n", "is_numeric_5digit                                                 True   \n", "\n", "                                                                            1  \\\n", "provider_id                                                            MF4252   \n", "institution_name                                                         BDSC   \n", "case_type                                                     Outpatient Case   \n", "claim_id                                                           **********   \n", "claim_net                                                               221.0   \n", "unique_id                                                    MF4252**********   \n", "case                                                               **********   \n", "insurance_plan_id                                                      700000   \n", "plan_name                                             COMPREHENSIVE 3 - ALDAR   \n", "network_name                                                     ALDAR-COMP 3   \n", "payer_id                                                                 A001   \n", "payer_id_desc                                                 Daman Insurance   \n", "id_payer                                                             2.12E+11   \n", "denial_code                                                               NaN   \n", "code_activity                                                           99203   \n", "activity_desc               Office or other outpatient visit for the evalu...   \n", "activity_id                                                        **********   \n", "reference_activity                                                  154682709   \n", "start_activity_date                                                16/06/2023   \n", "type_activity                                                               3   \n", "act_type_desc                                                             CPT   \n", "activity_quantity                                                         1.0   \n", "mapping_status                                                     Fully Paid   \n", "claim_mapping_status                                               Fully Paid   \n", "gross                                                                   142.0   \n", "patient_share                                                            50.0   \n", "net                                                                      92.0   \n", "payment_amount                                                           92.0   \n", "rejected_amount                                                           0.0   \n", "resub_net                                                                 0.0   \n", "clinician                                                             GD11650   \n", "clinician_name                                                PRASANNA SHETTY   \n", "resub_date                                                                NaN   \n", "remittance_date                                                    07/10/2023   \n", "ra_aging                                                                  110   \n", "resub_aging                                                               564   \n", "claim_status_desc                                                         NaN   \n", "resub_type_desc                                                           NaN   \n", "encounter_start_type                                                        1   \n", "encounter_start_type_desc            Elective, i.e., an Encounter is schedule   \n", "encounter_start_date                                               16/06/2023   \n", "encounter_end_date                                                 16/06/2023   \n", "encounter_end_type                                                        NaN   \n", "encounter_end_type_desc                                                   NaN   \n", "receiver_id                                                              A001   \n", "receiver_id_desc                                              Daman Insurance   \n", "prior_authorization                                                       NaN   \n", "submission_date                                                    19/06/2023   \n", "processing_status                                                         NaN   \n", "accepted_type                                                             NaN   \n", "accepted_type_reason_items                                                NaN   \n", "reconciliation_claim_tag                                                   No   \n", "year_encounter_end_date                                                  2023   \n", "aio_patient_id                                                       AIO00001   \n", "is_numeric_5digit                                                        True   \n", "\n", "                                                                            2  \n", "provider_id                                                            MF4252  \n", "institution_name                                                         BDSC  \n", "case_type                                                     Outpatient Case  \n", "claim_id                                                           **********  \n", "claim_net                                                                92.0  \n", "unique_id                                                    MF4252**********  \n", "case                                                               **********  \n", "insurance_plan_id                                                      700000  \n", "plan_name                                             COMPREHENSIVE 3 - ALDAR  \n", "network_name                                                     ALDAR-COMP 3  \n", "payer_id                                                                 A001  \n", "payer_id_desc                                                 Daman Insurance  \n", "id_payer                                                             2.12E+11  \n", "denial_code                                                               NaN  \n", "code_activity                                                           99203  \n", "activity_desc               Office or other outpatient visit for the evalu...  \n", "activity_id                                                        **********  \n", "reference_activity                                                  154658090  \n", "start_activity_date                                                16/06/2023  \n", "type_activity                                                               3  \n", "act_type_desc                                                             CPT  \n", "activity_quantity                                                         1.0  \n", "mapping_status                                                     Fully Paid  \n", "claim_mapping_status                                               Fully Paid  \n", "gross                                                                   142.0  \n", "patient_share                                                            50.0  \n", "net                                                                      92.0  \n", "payment_amount                                                           92.0  \n", "rejected_amount                                                           0.0  \n", "resub_net                                                                 0.0  \n", "clinician                                                             GD25783  \n", "clinician_name                                                   <PERSON><PERSON>  \n", "resub_date                                                                NaN  \n", "remittance_date                                                    28/07/2023  \n", "ra_aging                                                                   39  \n", "resub_aging                                                               635  \n", "claim_status_desc                                                         NaN  \n", "resub_type_desc                                                           NaN  \n", "encounter_start_type                                                        1  \n", "encounter_start_type_desc            Elective, i.e., an Encounter is schedule  \n", "encounter_start_date                                               16/06/2023  \n", "encounter_end_date                                                 16/06/2023  \n", "encounter_end_type                                                        NaN  \n", "encounter_end_type_desc                                                   NaN  \n", "receiver_id                                                              A001  \n", "receiver_id_desc                                              Daman Insurance  \n", "prior_authorization                                                       NaN  \n", "submission_date                                                    19/06/2023  \n", "processing_status                                                         NaN  \n", "accepted_type                                                             NaN  \n", "accepted_type_reason_items                                                NaN  \n", "reconciliation_claim_tag                                                   No  \n", "year_encounter_end_date                                                  2023  \n", "aio_patient_id                                                       AIO00002  \n", "is_numeric_5digit                                                        True  "]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Quick sample of the data\n", "print(\"👀 SAMPLE DATA (First 3 rows)\")\n", "print(\"=\" * 50)\n", "display(df.head(3).T)"]}, {"cell_type": "markdown", "id": "7d5d5490", "metadata": {}, "source": ["## 2. Data Quality Assessment"]}, {"cell_type": "code", "execution_count": 24, "id": "65d1af9e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔍 MISSING DATA ANALYSIS\n", "==================================================\n", "Columns with missing data:\n", "  • accepted_type_reason_items: 4,912.0 (98.3%)\n", "  • resub_type_desc: 4,651.0 (93.0%)\n", "  • claim_status_desc: 4,292.0 (85.9%)\n", "  • accepted_type: 4,281.0 (85.6%)\n", "  • processing_status: 4,281.0 (85.6%)\n", "  • denial_code: 4,122.0 (82.5%)\n", "  • encounter_end_type_desc: 4,117.0 (82.4%)\n", "  • encounter_end_type: 4,117.0 (82.4%)\n", "  • resub_date: 3,853.0 (77.1%)\n", "  • prior_authorization: 3,294.0 (65.9%)\n", "  • activity_desc: 81.0 (1.6%)\n", "  • clinician_name: 12.0 (0.2%)\n", "  • id_payer: 6.0 (0.1%)\n", "  • remittance_date: 6.0 (0.1%)\n"]}], "source": ["# Check for missing values\n", "print(\"🔍 MISSING DATA ANALYSIS\")\n", "print(\"=\" * 50)\n", "\n", "missing_data = df.isnull().sum()\n", "missing_pct = (missing_data / len(df)) * 100\n", "\n", "missing_summary = pd.DataFrame({\n", "    'Missing_Count': missing_data,\n", "    'Missing_Percentage': missing_pct\n", "}).sort_values('Missing_Count', ascending=False)\n", "\n", "# Show only columns with missing data\n", "missing_with_nulls = missing_summary[missing_summary['Missing_Count'] > 0]\n", "\n", "if len(missing_with_nulls) > 0:\n", "    print(\"Columns with missing data:\")\n", "    for col, row in missing_with_nulls.iterrows():\n", "        print(f\"  • {col}: {row['Missing_Count']:,} ({row['Missing_Percentage']:.1f}%)\")\n", "else:\n", "    print(\"✅ No missing data found!\")"]}, {"cell_type": "code", "execution_count": 25, "id": "0a5aee34", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔄 DUPLICATE ANALYSIS\n", "==================================================\n", "Exact duplicate rows: 0\n", "Duplicate activity IDs: 0\n", "✅ All activity IDs are unique\n"]}], "source": ["# Check for duplicate records\n", "print(\"🔄 DUPLICATE ANALYSIS\")\n", "print(\"=\" * 50)\n", "\n", "total_duplicates = df.duplicated().sum()\n", "print(f\"Exact duplicate rows: {total_duplicates:,}\")\n", "\n", "# Check for duplicate activity IDs (should be unique)\n", "if 'activity_id' in df.columns:\n", "    duplicate_activity_ids = df['activity_id'].duplicated().sum()\n", "    print(f\"Duplicate activity IDs: {duplicate_activity_ids:,}\")\n", "    \n", "    if duplicate_activity_ids == 0:\n", "        print(\"✅ All activity IDs are unique\")\n", "    else:\n", "        print(\"⚠️  Some activity IDs are duplicated\")"]}, {"cell_type": "markdown", "id": "996ffe21", "metadata": {}, "source": ["## 3. Patient and Case Analysis\n", "Understanding the core entities in our data"]}, {"cell_type": "code", "execution_count": 33, "id": "65c47ceb", "metadata": {}, "outputs": [{"data": {"text/plain": ["0       **********\n", "1       **********\n", "2       **********\n", "3       **********\n", "4       **********\n", "           ...    \n", "4994      17587575\n", "4995      17587575\n", "4996      17581149\n", "4997      17582237\n", "4998      17582237\n", "Name: case, Length: 4999, dtype: int64"]}, "execution_count": 33, "metadata": {}, "output_type": "execute_result"}], "source": ["df['case']"]}, {"cell_type": "code", "execution_count": 7, "id": "2acdff95", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["👥 PATIENT ANALYSIS\n", "==================================================\n", "Unique patients: 596\n", "Unique cases/encounters: 1,461\n", "Total activities/procedures: 4,999\n", "\n", "Average activities per patient: 8.4\n", "Average activities per case: 3.4\n", "Average cases per patient: 2.5\n"]}], "source": ["# Analyze patients and cases\n", "print(\"👥 PATIENT ANALYSIS\")\n", "print(\"=\" * 50)\n", "\n", "unique_patients = df['aio_patient_id'].nunique()\n", "unique_cases = df['case'].nunique() \n", "total_activities = len(df)\n", "\n", "print(f\"Unique patients: {unique_patients:,}\")\n", "print(f\"Unique cases/encounters: {unique_cases:,}\")\n", "print(f\"Total activities/procedures: {total_activities:,}\")\n", "print()\n", "print(f\"Average activities per patient: {total_activities / unique_patients:.1f}\")\n", "print(f\"Average activities per case: {total_activities / unique_cases:.1f}\")\n", "print(f\"Average cases per patient: {unique_cases / unique_patients:.1f}\")"]}, {"cell_type": "code", "execution_count": 8, "id": "46a9f769", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📈 ACTIVITIES PER PATIENT DISTRIBUTION\n", "==================================================\n", "Minimum activities: 1\n", "Maximum activities: 150\n", "Median activities: 3\n", "Mean activities: 8.4\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Analyze activity distribution per patient\n", "activities_per_patient = df.groupby('aio_patient_id').size()\n", "\n", "print(\"📈 ACTIVITIES PER PATIENT DISTRIBUTION\")\n", "print(\"=\" * 50)\n", "print(f\"Minimum activities: {activities_per_patient.min()}\")\n", "print(f\"Maximum activities: {activities_per_patient.max()}\")\n", "print(f\"Median activities: {activities_per_patient.median():.0f}\")\n", "print(f\"Mean activities: {activities_per_patient.mean():.1f}\")\n", "\n", "# Create a simple histogram\n", "plt.figure(figsize=(10, 6))\n", "plt.hist(activities_per_patient, bins=30, edgecolor='black', alpha=0.7)\n", "plt.title('Distribution of Activities per Patient')\n", "plt.xlabel('Number of Activities')\n", "plt.ylabel('Number of Patients')\n", "plt.grid(True, alpha=0.3)\n", "plt.show()"]}, {"cell_type": "markdown", "id": "f4104f06", "metadata": {}, "source": ["## 4. Medical Codes Analysis\n", "Understanding the procedures and services in our data"]}, {"cell_type": "code", "execution_count": 9, "id": "fa53b782", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🩺 MEDICAL CODES ANALYSIS\n", "==================================================\n", "Unique medical codes: 769\n", "5-digit numeric codes (CPT-like): 3,230 (64.6%)\n", "Other code formats: 1,769 (35.4%)\n"]}], "source": ["# Analyze medical codes\n", "print(\"🩺 MEDICAL CODES ANALYSIS\")\n", "print(\"=\" * 50)\n", "\n", "unique_codes = df['code_activity'].nunique()\n", "print(f\"Unique medical codes: {unique_codes:,}\")\n", "\n", "# Check if codes look like CPT codes (5-digit numbers)\n", "df['is_numeric_5digit'] = df['code_activity'].astype(str).str.match(r'^\\d{5}$')\n", "cpt_like_codes = df['is_numeric_5digit'].sum()\n", "\n", "print(f\"5-digit numeric codes (CPT-like): {cpt_like_codes:,} ({cpt_like_codes/len(df)*100:.1f}%)\")\n", "print(f\"Other code formats: {len(df) - cpt_like_codes:,} ({(len(df) - cpt_like_codes)/len(df)*100:.1f}%)\")"]}, {"cell_type": "code", "execution_count": 10, "id": "ddc3930d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🏆 TOP 10 MOST COMMON MEDICAL CODES\n", "==================================================\n", " 1. A4649: 470 times (9.4%) - Syringe, Disposable, 20ML Box50...\n", " 2. 99213: 462 times (9.2%) - Office or other outpatient visit for the evaluatio...\n", " 3. 97110: 231 times (4.6%) - Therapeutic procedure, 1 or more areas,each 15 min...\n", " 4. 97140: 225 times (4.5%) - Manual therapy techniques (eg, mobilization/ manip...\n", " 5. 97014: 220 times (4.4%) - Application of a modality to 1 or more areas; elec...\n", " 6. 99214: 195 times (3.9%) - Office or other outpatient visit for the evaluatio...\n", " 7. 99203: 147 times (2.9%) - Office or other outpatient visit for the evaluatio...\n", " 8. 99212: 114 times (2.3%) - Office or other outpatient visit for the evaluatio...\n", " 9. 97010: 88 times (1.8%) - Application of a modality to 1 or more areas; hot ...\n", "10. 99204: 86 times (1.7%) - Office or other outpatient visit for the evaluatio...\n"]}], "source": ["# Show most common codes\n", "print(\"🏆 TOP 10 MOST COMMON MEDICAL CODES\")\n", "print(\"=\" * 50)\n", "\n", "top_codes = df['code_activity'].value_counts().head(10)\n", "for i, (code, count) in enumerate(top_codes.items(), 1):\n", "    pct = (count / len(df)) * 100\n", "    # Get a sample description\n", "    desc = df[df['code_activity'] == code]['activity_desc'].iloc[0]\n", "    print(f\"{i:2d}. {code}: {count:,} times ({pct:.1f}%) - {desc[:50]}...\")"]}, {"cell_type": "code", "execution_count": 11, "id": "6ecaf3a4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔍 CODE PATTERN ANALYSIS\n", "==================================================\n", "Sample codes to understand patterns:\n", "  • 87880\n", "  • 99203\n", "  • 97014\n", "  • 97110\n", "  • 97140\n", "  • 97161\n", "  • 99204\n", "  • 17110\n", "  • 99213\n", "  • 99212\n", "  • 69210\n", "  • 97010\n", "  • 99214\n", "  • 72100\n", "  • 71046\n", "  • 94010\n", "  • 94640\n", "  • B46-4387-00778-01\n", "  • C81-0459-03093-01\n", "  • 73610\n", "\n", "Code length distribution:\n", "  2 characters: 5 codes (0.1%)\n", "  3 characters: 6 codes (0.1%)\n", "  4 characters: 28 codes (0.6%)\n", "  5 characters: 3,773 codes (75.5%)\n", "  6 characters: 33 codes (0.7%)\n", "  17 characters: 1,154 codes (23.1%)\n"]}], "source": ["# Analyze code patterns\n", "print(\"🔍 CODE PATTERN ANALYSIS\")\n", "print(\"=\" * 50)\n", "\n", "# Sample different code formats\n", "sample_codes = df['code_activity'].unique()[:20]\n", "print(\"Sample codes to understand patterns:\")\n", "for code in sample_codes:\n", "    print(f\"  • {code}\")\n", "\n", "# Check code lengths\n", "code_lengths = df['code_activity'].astype(str).str.len()\n", "print(f\"\\nCode length distribution:\")\n", "length_counts = code_lengths.value_counts().sort_index()\n", "for length, count in length_counts.head(10).items():\n", "    pct = (count / len(df)) * 100\n", "    print(f\"  {length} characters: {count:,} codes ({pct:.1f}%)\")"]}, {"cell_type": "markdown", "id": "c935767c", "metadata": {}, "source": ["## 5. Financial Information Analysis\n", "Understanding the cost and payment data"]}, {"cell_type": "code", "execution_count": 12, "id": "17c83368", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["💰 FINANCIAL DATA ANALYSIS\n", "==================================================\n", "Available financial columns: ['gross', 'net', 'patient_share', 'payment_amount']\n", "\n", "GROSS:\n", "  Records with data: 4,999 (100.0%)\n", "  Total amount: $705,006.09\n", "  Average: $141.03\n", "  Median: $52.20\n", "  Range: $0.00 - $16000.00\n", "  Zero values: 1,080\n", "\n", "NET:\n", "  Records with data: 4,999 (100.0%)\n", "  Total amount: $660,289.32\n", "  Average: $132.08\n", "  Median: $45.00\n", "  Range: $0.00 - $16000.00\n", "  Zero values: 1,080\n", "\n", "PATIENT_SHARE:\n", "  Records with data: 4,999 (100.0%)\n", "  Total amount: $44,716.77\n", "  Average: $8.95\n", "  Median: $0.00\n", "  Range: $0.00 - $193.50\n", "  Zero values: 3,491\n", "\n", "PAYMENT_AMOUNT:\n", "  Records with data: 4,999 (100.0%)\n", "  Total amount: $545,183.74\n", "  Average: $109.06\n", "  Median: $33.35\n", "  Range: $0.00 - $16000.00\n", "  Zero values: 1,479\n"]}], "source": ["# Analyze financial columns\n", "financial_cols = ['gross', 'net', 'patient_share', 'payment_amount']\n", "available_financial = [col for col in financial_cols if col in df.columns]\n", "\n", "print(\"💰 FINANCIAL DATA ANALYSIS\")\n", "print(\"=\" * 50)\n", "print(f\"Available financial columns: {available_financial}\")\n", "\n", "for col in available_financial:\n", "    data = df[col].dropna()\n", "    if len(data) > 0:\n", "        print(f\"\\n{col.upper()}:\")\n", "        print(f\"  Records with data: {len(data):,} ({len(data)/len(df)*100:.1f}%)\")\n", "        print(f\"  Total amount: ${data.sum():,.2f}\")\n", "        print(f\"  Average: ${data.mean():.2f}\")\n", "        print(f\"  Median: ${data.median():.2f}\")\n", "        print(f\"  Range: ${data.min():.2f} - ${data.max():.2f}\")\n", "        print(f\"  Zero values: {(data == 0).sum():,}\")"]}, {"cell_type": "code", "execution_count": 13, "id": "31140647", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1200x500 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "💵 COST RANGE ANALYSIS\n", "==============================\n", "Free services ($0): 1,080\n", "Low cost ($1-$100): 2,388\n", "Medium cost ($101-$500): 1,332\n", "High cost ($501-$1,000): 101\n", "Very high cost ($1,000+): 98\n"]}], "source": ["# Visualize financial distribution\n", "if 'gross' in df.columns:\n", "    plt.figure(figsize=(12, 5))\n", "    \n", "    # Histogram of gross amounts\n", "    plt.subplot(1, 2, 1)\n", "    gross_data = df['gross'].dropna()\n", "    plt.hist(gross_data, bins=50, edgecolor='black', alpha=0.7)\n", "    plt.title('Distribution of Gross Amounts')\n", "    plt.xlabel('Amount ($)')\n", "    plt.ylabel('Frequency')\n", "    plt.yscale('log')  # Log scale for better visualization\n", "    \n", "    # Box plot\n", "    plt.subplot(1, 2, 2)\n", "    plt.boxplot(gross_data)\n", "    plt.title('Gross Amount Box Plot')\n", "    plt.ylabel('Amount ($)')\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    # Summary statistics for cost ranges\n", "    print(\"\\n💵 COST RANGE ANALYSIS\")\n", "    print(\"=\" * 30)\n", "    print(f\"Free services ($0): {(gross_data == 0).sum():,}\")\n", "    print(f\"Low cost ($1-$100): {((gross_data > 0) & (gross_data <= 100)).sum():,}\")\n", "    print(f\"Medium cost ($101-$500): {((gross_data > 100) & (gross_data <= 500)).sum():,}\")\n", "    print(f\"High cost ($501-$1,000): {((gross_data > 500) & (gross_data <= 1000)).sum():,}\")\n", "    print(f\"Very high cost ($1,000+): {(gross_data > 1000).sum():,}\")"]}, {"cell_type": "markdown", "id": "4aace4e8", "metadata": {}, "source": ["## 6. Healthcare Providers Analysis\n", "Understanding who provides the services"]}, {"cell_type": "code", "execution_count": 14, "id": "7700f7c6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["👨‍⚕️ HEALTHCARE PROVIDERS ANALYSIS\n", "==================================================\n", "Unique clinicians: 270\n", "Records with clinician info: 4,999 (100.0%)\n", "\n", "Top 10 most active clinicians:\n", " 1. GD10670: 269 activities (5.4%)\n", " 2. GD21895: 256 activities (5.1%)\n", " 3. GD5891: 250 activities (5.0%)\n", " 4. GD20346: 179 activities (3.6%)\n", " 5. GD25091: 156 activities (3.1%)\n", " 6. GD8357: 134 activities (2.7%)\n", " 7. GD37430: 102 activities (2.0%)\n", " 8. GD25783: 95 activities (1.9%)\n", " 9. GD24090: 94 activities (1.9%)\n", "10. GD20029: 93 activities (1.9%)\n", "\n", "Unique institutions: 10\n", "\n", "Institutions:\n", "  • BDSC: 2,934 activities (58.7%)\n", "  • BURJEEL-AL AIN: 1,075 activities (21.5%)\n", "  • BURJEEL-AD: 586 activities (11.7%)\n", "  • BURJEEL ASHAREJ: 176 activities (3.5%)\n", "  • BMC-SHAMKHA: 162 activities (3.2%)\n", "  • LLH MC -MS: 28 activities (0.6%)\n", "  • LLH OASIS: 16 activities (0.3%)\n", "  • BURJEEL-SHARJAH: 11 activities (0.2%)\n", "  • LLH-MS: 8 activities (0.2%)\n", "  • BMC-BARARI: 3 activities (0.1%)\n"]}], "source": ["# Analyze healthcare providers\n", "print(\"👨‍⚕️ HEALTHCARE PROVIDERS ANALYSIS\")\n", "print(\"=\" * 50)\n", "\n", "if 'clinician' in df.columns:\n", "    unique_clinicians = df['clinician'].nunique()\n", "    records_with_clinician = df['clinician'].notna().sum()\n", "    \n", "    print(f\"Unique clinicians: {unique_clinicians:,}\")\n", "    print(f\"Records with clinician info: {records_with_clinician:,} ({records_with_clinician/len(df)*100:.1f}%)\")\n", "    \n", "    # Top clinicians by volume\n", "    top_clinicians = df['clinician'].value_counts().head(10)\n", "    print(f\"\\nTop 10 most active clinicians:\")\n", "    for i, (clinician, count) in enumerate(top_clinicians.items(), 1):\n", "        pct = (count / len(df)) * 100\n", "        print(f\"{i:2d}. {clinician}: {count:,} activities ({pct:.1f}%)\")\n", "\n", "if 'institution_name' in df.columns:\n", "    unique_institutions = df['institution_name'].nunique()\n", "    print(f\"\\nUnique institutions: {unique_institutions:,}\")\n", "    \n", "    # Show all institutions if not too many\n", "    if unique_institutions <= 15:\n", "        institutions = df['institution_name'].value_counts()\n", "        print(f\"\\nInstitutions:\")\n", "        for institution, count in institutions.items():\n", "            pct = (count / len(df)) * 100\n", "            print(f\"  • {institution}: {count:,} activities ({pct:.1f}%)\")"]}, {"cell_type": "markdown", "id": "c3773c09", "metadata": {}, "source": ["## 7. Date and Time Analysis\n", "Understanding the temporal aspects of our data"]}, {"cell_type": "code", "execution_count": 15, "id": "4c80305f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📅 DATE ANALYSIS\n", "==================================================\n", "Date columns found: ['start_activity_date', 'resub_date', 'remittance_date', 'encounter_start_date', 'encounter_end_date', 'submission_date', 'year_encounter_end_date']\n", "\n", "start_activity_date:\n", "  Records with dates: 4,999 (100.0%)\n", "  Sample dates: ['16/06/2023', '16/06/2023', '16/06/2023', '16/06/2023', '14/06/2023']\n", "\n", "resub_date:\n", "  Records with dates: 1,146 (22.9%)\n", "  Sample dates: ['30/09/2023', '30/09/2023', '30/09/2023', '30/09/2023', '30/09/2023']\n", "\n", "remittance_date:\n", "  Records with dates: 4,993 (99.9%)\n", "  Sample dates: ['07/10/2023', '07/10/2023', '28/07/2023', '28/07/2023', '28/07/2023']\n", "\n", "encounter_start_date:\n", "  Records with dates: 4,999 (100.0%)\n", "  Sample dates: ['16/06/2023', '16/06/2023', '16/06/2023', '16/06/2023', '14/06/2023']\n", "\n", "encounter_end_date:\n", "  Records with dates: 4,999 (100.0%)\n", "  Sample dates: ['16/06/2023', '16/06/2023', '16/06/2023', '16/06/2023', '14/06/2023']\n", "\n", "submission_date:\n", "  Records with dates: 4,999 (100.0%)\n", "  Sample dates: ['19/06/2023', '19/06/2023', '19/06/2023', '19/06/2023', '19/06/2023']\n", "\n", "year_encounter_end_date:\n", "  Records with dates: 4,999 (100.0%)\n", "  Sample dates: [2023, 2023, 2023, 2023, 2023]\n"]}], "source": ["# Analyze date columns\n", "date_columns = [col for col in df.columns if 'date' in col.lower()]\n", "\n", "print(\"📅 DATE ANALYSIS\")\n", "print(\"=\" * 50)\n", "print(f\"Date columns found: {date_columns}\")\n", "\n", "for col in date_columns:\n", "    if col in df.columns:\n", "        print(f\"\\n{col}:\")\n", "        non_null_dates = df[col].notna().sum()\n", "        print(f\"  Records with dates: {non_null_dates:,} ({non_null_dates/len(df)*100:.1f}%)\")\n", "        \n", "        if non_null_dates > 0:\n", "            # Sample of date formats\n", "            sample_dates = df[col].dropna().head(5).tolist()\n", "            print(f\"  Sample dates: {sample_dates}\")"]}, {"cell_type": "code", "execution_count": 16, "id": "83a9aac0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🏥 ENCOUNTER PATTERNS\n", "==================================================\n", "Total encounters with dates: 4,999\n", "Same-day encounters: 4,908 (98.2%)\n", "Multi-day encounters: 91 (1.8%)\n"]}], "source": ["# If we have encounter dates, analyze patterns\n", "if 'encounter_start_date' in df.columns and 'encounter_end_date' in df.columns:\n", "    encounter_data = df[['encounter_start_date', 'encounter_end_date']].dropna()\n", "    \n", "    if len(encounter_data) > 0:\n", "        print(\"🏥 ENCOUNTER PATTERNS\")\n", "        print(\"=\" * 50)\n", "        \n", "        # Check for same-day vs multi-day encounters\n", "        same_start_end = (encounter_data['encounter_start_date'] == encounter_data['encounter_end_date']).sum()\n", "        \n", "        print(f\"Total encounters with dates: {len(encounter_data):,}\")\n", "        print(f\"Same-day encounters: {same_start_end:,} ({same_start_end/len(encounter_data)*100:.1f}%)\")\n", "        print(f\"Multi-day encounters: {len(encounter_data)-same_start_end:,} ({(len(encounter_data)-same_start_end)/len(encounter_data)*100:.1f}%)\")"]}, {"cell_type": "markdown", "id": "834a8790", "metadata": {}, "source": ["## 8. OMOP CDM Mapping Potential\n", "Understanding how our data could fit into OMOP Common Data Model"]}, {"cell_type": "code", "execution_count": 17, "id": "7d394e0c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🎯 OMOP CDM MAPPING ASSESSMENT\n", "==================================================\n", "The OMOP Common Data Model has several key tables. Let's see how our data fits:\n", "\n", "👥 PERSON Table:\n", "   Purpose: Store patient demographics\n", "   Our data: ✅ aio_patient_id (can be person_id)\n", "   Missing: ❌ birth_date, gender, race (common in anonymized data)\n", "   Status: Basic implementation possible\n", "\n", "🏥 VISIT_OCCURRENCE Table:\n", "   Purpose: Store healthcare encounters\n", "   Our data: ✅ case (visit_occurrence_id), encounter dates\n", "   Our data: ✅ case_type (visit_type)\n", "   Status: Excellent mapping potential\n", "\n", "🩺 PROCEDURE_OCCURRENCE Table:\n", "   Purpose: Store medical procedures and services\n", "   Our data: ✅ code_activity (procedure codes)\n", "   Our data: ✅ activity_desc (procedure descriptions)\n", "   Our data: ✅ start_activity_date (procedure_date)\n", "   Code quality: 64.6% appear to be standard CPT codes\n", "   Status: Strong mapping potential\n", "\n", "💰 COST Table:\n", "   Purpose: Store financial information\n", "   Our data: ✅ gross (total_charge)\n", "   Our data: ✅ payment_amount (total_paid)\n", "   Our data: ✅ patient_share (patient_copay)\n", "   Status: Excellent financial data available\n", "\n", "👨‍⚕️ PROVIDER Table:\n", "   Purpose: Store healthcare provider information\n", "   Our data: ✅ clinician (provider_name)\n", "   Missing: ❌ provider specialty, credentials\n", "   Status: Basic provider tracking possible\n"]}], "source": ["# OMOP mapping assessment - simple and clear\n", "print(\"🎯 OMOP CDM MAPPING ASSESSMENT\")\n", "print(\"=\" * 50)\n", "\n", "print(\"The OMOP Common Data Model has several key tables. Let's see how our data fits:\")\n", "print()\n", "\n", "# PERSON table potential\n", "print(\"👥 PERSON Table:\")\n", "print(\"   Purpose: Store patient demographics\")\n", "print(\"   Our data: ✅ aio_patient_id (can be person_id)\")\n", "print(\"   Missing: ❌ birth_date, gender, race (common in anonymized data)\")\n", "print(\"   Status: Basic implementation possible\")\n", "print()\n", "\n", "# VISIT_OCCURRENCE potential  \n", "print(\"🏥 VISIT_OCCURRENCE Table:\")\n", "print(\"   Purpose: Store healthcare encounters\")\n", "print(\"   Our data: ✅ case (visit_occurrence_id), encounter dates\")\n", "if 'case_type' in df.columns:\n", "    print(\"   Our data: ✅ case_type (visit_type)\")\n", "print(\"   Status: Excellent mapping potential\")\n", "print()\n", "\n", "# PROCEDURE_OCCURRENCE potential\n", "print(\"🩺 PROCEDURE_OCCURRENCE Table:\")\n", "print(\"   Purpose: Store medical procedures and services\")\n", "print(\"   Our data: ✅ code_activity (procedure codes)\")\n", "print(\"   Our data: ✅ activity_desc (procedure descriptions)\")\n", "print(\"   Our data: ✅ start_activity_date (procedure_date)\")\n", "print(f\"   Code quality: {cpt_like_codes/len(df)*100:.1f}% appear to be standard CPT codes\")\n", "print(\"   Status: Strong mapping potential\")\n", "print()\n", "\n", "# COST table potential\n", "print(\"💰 COST Table:\")\n", "print(\"   Purpose: Store financial information\")\n", "if 'gross' in df.columns:\n", "    print(\"   Our data: ✅ gross (total_charge)\")\n", "if 'payment_amount' in df.columns:\n", "    print(\"   Our data: ✅ payment_amount (total_paid)\")\n", "if 'patient_share' in df.columns:\n", "    print(\"   Our data: ✅ patient_share (patient_copay)\")\n", "print(\"   Status: Excellent financial data available\")\n", "print()\n", "\n", "# PROVIDER table potential\n", "print(\"👨‍⚕️ PROVIDER Table:\")\n", "print(\"   Purpose: Store healthcare provider information\")\n", "if 'clinician' in df.columns:\n", "    print(\"   Our data: ✅ clinician (provider_name)\")\n", "print(\"   Missing: ❌ provider specialty, credentials\")\n", "print(\"   Status: Basic provider tracking possible\")"]}, {"cell_type": "code", "execution_count": 18, "id": "6bbc2b6b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📊 OMOP IMPLEMENTATION READINESS SUMMARY\n", "==================================================\n", "Table readiness scores (0-100%):\n", "  PERSON: 60% 🟡 MEDIUM\n", "  VISIT_OCCURRENCE: 85% 🟢 HIGH\n", "  PROCEDURE_OCCURRENCE: 80% 🟢 HIGH\n", "  COST: 90% 🟢 HIGH\n", "  PROVIDER: 65% 🟡 MEDIUM\n", "  DRUG_EXPOSURE: 10% 🔴 LOW\n", "  CONDITION_OCCURRENCE: 20% 🔴 LOW\n", "\n", "Overall assessment:\n", "  Average readiness: 59%\n", "  Primary strength: Financial and procedure data\n", "  Main limitation: Missing clinical details (diagnoses, medications)\n", "  Recommendation: Focus on procedure and cost analysis initially\n"]}], "source": ["# Summary of data readiness for OMOP\n", "print(\"📊 OMOP IMPLEMENTATION READINESS SUMMARY\")\n", "print(\"=\" * 50)\n", "\n", "readiness_scores = {\n", "    'PERSON': 60,  # Basic patient ID only\n", "    'VISIT_OCCURRENCE': 85,  # Good encounter data\n", "    'PROCEDURE_OCCURRENCE': 80,  # Good procedure codes and dates\n", "    'COST': 90,  # Excellent financial data\n", "    'PROVIDER': 65,  # Basic provider info\n", "    'DRUG_EXPOSURE': 10,  # No medication data visible\n", "    'CONDITION_OCCURRENCE': 20,  # No diagnosis codes visible\n", "}\n", "\n", "print(\"Table readiness scores (0-100%):\")\n", "for table, score in readiness_scores.items():\n", "    if score >= 70:\n", "        status = \"🟢 HIGH\"\n", "    elif score >= 50:\n", "        status = \"🟡 MEDIUM\" \n", "    else:\n", "        status = \"🔴 LOW\"\n", "    print(f\"  {table}: {score}% {status}\")\n", "\n", "print(f\"\\nOverall assessment:\")\n", "avg_score = sum(readiness_scores.values()) / len(readiness_scores)\n", "print(f\"  Average readiness: {avg_score:.0f}%\")\n", "print(f\"  Primary strength: Financial and procedure data\")\n", "print(f\"  Main limitation: Missing clinical details (diagnoses, medications)\")\n", "print(f\"  Recommendation: Focus on procedure and cost analysis initially\")"]}, {"cell_type": "markdown", "id": "997293b6", "metadata": {}, "source": ["## 9. Key Insights and Next Steps"]}, {"cell_type": "code", "execution_count": 19, "id": "8d703595", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🎯 KEY INSIGHTS\n", "==================================================\n", "📈 Data Volume:\n", "  • 596 patients with 4,999 healthcare activities\n", "  • 1,461 unique encounters/cases\n", "  • Average 8.4 activities per patient\n", "\n", "🩺 Medical Procedures:\n", "  • 769 unique medical codes\n", "  • 64.6% appear to be standard CPT codes\n", "  • 35.4% use local/regional coding\n", "\n", "💰 Financial Impact:\n", "  • Total healthcare value: $705,006.09\n", "  • Average cost per activity: $141.03\n", "\n", "🏥 Healthcare Delivery:\n", "  • 270 healthcare providers\n", "  • 10 healthcare institutions\n", "\n", "🎯 OMOP Transformation Priority:\n", "  1. Start with PROCEDURE_OCCURRENCE (strong code data)\n", "  2. Implement COST table (excellent financial data)\n", "  3. Add VISIT_OCCURRENCE (good encounter structure)\n", "  4. Create basic PERSON records (patient IDs only)\n", "  5. Later: enhance with external code mappings\n"]}], "source": ["print(\"🎯 KEY INSIGHTS\")\n", "print(\"=\" * 50)\n", "\n", "print(\"📈 Data Volume:\")\n", "print(f\"  • {unique_patients:,} patients with {total_activities:,} healthcare activities\")\n", "print(f\"  • {unique_cases:,} unique encounters/cases\")\n", "print(f\"  • Average {total_activities/unique_patients:.1f} activities per patient\")\n", "\n", "print(f\"\\n🩺 Medical Procedures:\")\n", "print(f\"  • {unique_codes:,} unique medical codes\")\n", "print(f\"  • {cpt_like_codes/len(df)*100:.1f}% appear to be standard CPT codes\")\n", "print(f\"  • {(len(df) - cpt_like_codes)/len(df)*100:.1f}% use local/regional coding\")\n", "\n", "if 'gross' in df.columns:\n", "    total_value = df['gross'].sum()\n", "    print(f\"\\n💰 Financial Impact:\")\n", "    print(f\"  • Total healthcare value: ${total_value:,.2f}\")\n", "    print(f\"  • Average cost per activity: ${df['gross'].mean():.2f}\")\n", "\n", "print(f\"\\n🏥 Healthcare Delivery:\")\n", "if 'clinician' in df.columns:\n", "    print(f\"  • {unique_clinicians:,} healthcare providers\")\n", "if 'institution_name' in df.columns:\n", "    print(f\"  • {unique_institutions:,} healthcare institutions\")\n", "\n", "print(f\"\\n🎯 OMOP Transformation Priority:\")\n", "print(f\"  1. Start with PROCEDURE_OCCURRENCE (strong code data)\")\n", "print(f\"  2. Implement COST table (excellent financial data)\")\n", "print(f\"  3. Add VISIT_OCCURRENCE (good encounter structure)\")\n", "print(f\"  4. Create basic PERSON records (patient IDs only)\")\n", "print(f\"  5. Later: enhance with external code mappings\")"]}, {"cell_type": "markdown", "id": "06f9e7c1", "metadata": {}, "source": ["## Conclusion\n", "\n", "This Abu Dhabi claims dataset provides a solid foundation for OMOP CDM implementation, particularly strong in:\n", "\n", "- **Procedure tracking** with medical codes and descriptions\n", "- **Financial analysis** with comprehensive cost information  \n", "- **Encounter management** with clear case/visit structure\n", "- **Provider identification** with clinician information\n", "\n", "**Recommended approach:**\n", "1. Begin with procedure and cost analysis (highest data quality)\n", "2. Use external vocabularies to map local codes to OMOP concepts\n", "3. Implement core OMOP tables incrementally\n", "4. Focus on claims-based use cases initially\n", "\n", "**Next steps:**\n", "- Map procedure codes to OMOP concept IDs using vocabularies\n", "- Design ETL pipeline for priority OMOP tables\n", "- Validate data quality for production use"]}], "metadata": {"kernelspec": {"display_name": "fhir-omop", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}